<style scoped>
.data_monitoring_main {
    display: flex;
    justify-content: center;
    text-align: center;
    width: 20vw;
    height: 24.4vh;
}
.custom-scroll-board >>> .header-item {
    font-size: 14px !important; /* 设置你需要的字体大小 */
}
</style>

<template>
    <div class="data_monitoring_main">
        <dv-scroll-board class="custom-scroll-board" :config="deviceTableConfig" style="height: 18vh; width: 18vw" />
    </div>
</template>

<script>
export default {
    data() {
        return {
            // 设备表格配置
            deviceTableConfig: {
                header: ['设备ID', '位置', '状态', '最后上报'],

                headerFontSize: 1, // 表头字体大小
                headerHeight: 25, // 表头高度
                data: [
                    ['设备001', '区域A', '在线', '2023-10-01 10:00'],
                    ['设备002', '区域B', '离线', '2023-10-01 09:55'],
                    ['设备003', '区域C', '在线', '2023-10-01 10:05'],
                    ['设备004', '区域D', '离线', '2023-10-01 09:45'],
                    ['设备005', '区域E', '在线', '2023-10-01 10:10'],
                    ['设备006', '区域F', '离线', '2023-10-01 09:30'],
                    ['设备007', '区域G', '在线', '2023-10-01 10:15'],
                    ['设备008', '区域H', '离线', '2023-10-01 09:20'],
                ],
                rowNum: 8,
                headerBGC: '#1a3a6e',
                oddRowBGC: 'rgba(10, 29, 50, 0.8)',
                evenRowBGC: 'rgba(10, 29, 50, 0.5)',
                align: ['left', 'left', 'left', 'left'],
                headerAlign: ['left', 'left', 'left', 'left'], // 可以每列设置不同的对齐方式
            },
        };
    },
    components: {},
    created() {
        this.init();
    },
    methods: {
        init() {
            this.getServer();
        },
        /** 查询服务器信息 */
        getServer() {},
        drawPieCpu() {},
    },
};
</script>
