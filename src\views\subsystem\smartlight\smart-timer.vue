<template>
  <div style="width: 100%; height: calc(100vh - 50px); padding: 16px; background-color: #E9EFF9;">
    <div style="width: 100%; height: 100%; display: flex; flex-direction: column;">
      <el-row :gutter="16" style="flex: 0 0 10%;">
        <el-col :span="24">
          <div class="grid-content bg-purple" style="padding-right: 10px; justify-content:flex-end;">
            <!-- 假期安排 -->
            <el-button type="primary" @click="holidayOverview">假期总览</el-button>
            <el-button type="success" @click="modeSetting">定时计划</el-button>
            <el-button type="info" disabled>历史记录</el-button>
            <el-button type="warning" disabled>提醒通知</el-button>
            <el-button type="danger" disabled>数据备份</el-button>
          </div>
        </el-col>
      </el-row>
      <el-row :gutter="16" style="flex: 0 0 90%;">
        <el-col :span="18">
          <div class="grid-content bg-purple">
            <div style="width: 100; height: 100%; display: flex; flex-wrap: nowrap;">
              <el-calendar v-model="value">
                <template slot="dateCell" slot-scope="{date, data}">
                  <div @dblclick="addPlan()">
                    <p :class="data.isSelected ? 'is-selected' : ''">
                      {{ data.day.split('-').slice(-1)[0] }}
                      <span v-if="isHoliday(date)" style="color:red;">
                        ({{ getHolidayName(date) }})
                      </span>
                      <span v-if="isScheduled(date)" style="color:red;">
                        ({{ getScheduledName(date) }})
                      </span>
                    </p>
                  </div>
                </template>
              </el-calendar>
            </div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="grid-content bg-purple">
            <div style="width: 100%; height: 100%; background-color: #F0F;">
              <div style="width: 100%; height: 50%; background-color: beige;"></div>
              <div style="width: 100%; height: 50%; background-color:cadetblue;"></div>
            </div>
          </div>
        </el-col>
      </el-row>
    </div>

    <el-dialog :close-on-click-modal="false" title="新增定时计划" :visible.sync="dialogVisible" width="35%" append-to-body>
      <el-form :model="ruleForm" :rules="rules" ref="ruleForm" label-width="120px" class="demo-ruleForm">
        <el-form-item label="定时区域" prop="region">
          <el-col :span="22">
            <el-select v-model="ruleForm.region" placeholder="请选择区域">
              <el-option label="区域一" value="shanghai"></el-option>
              <el-option label="区域二" value="beijing"></el-option>
            </el-select>
          </el-col>
        </el-form-item>
        <el-form-item label="定时场景" prop="region">
          <el-col :span="22">
            <el-select v-model="ruleForm.region" placeholder="请选择场景">
              <el-option label="场景一" value="shanghai"></el-option>
              <el-option label="场景二" value="beijing"></el-option>
            </el-select>
          </el-col>
        </el-form-item>
        <el-form-item label="定时时间" required>
          <el-form-item prop="date1">
            <el-col :span="22">
              <el-date-picker style="width: 100%;" v-model="ruleForm.date1" type="datetimerange" range-separator="至"
                start-placeholder="开始日期" end-placeholder="结束日期">
              </el-date-picker>
            </el-col>
          </el-form-item>
        </el-form-item>
        <el-form-item label="执行方式" prop="resource">
          <el-radio-group v-model="ruleForm.resource">
            <el-radio label="设备感应"></el-radio>
            <el-radio label="直接执行"></el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="定时说明" prop="desc">
          <el-col :span="22">
            <el-input type="textarea" v-model="ruleForm.desc"></el-input>
          </el-col>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="dialogVisible = false">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
export default {
  data() {
    return {
      value: new Date(),
      holidays: [
        { start: '2024-01-01', end: '2024-01-01', name: '元旦' },
        { start: '2024-02-10', end: '2024-02-16', name: '春节' },
        { start: '2024-04-04', end: '2024-04-06', name: '清明节' },
        { start: '2024-05-01', end: '2024-05-05', name: '劳动节' },
        { start: '2024-06-25', end: '2024-06-25', name: '端午节' },
        { start: '2024-10-01', end: '2024-10-07', name: '国庆节' },
      ],
      schedules: [
        { start: '2024-10-15', end: '2024-10-15', name: '节能模式' },
        { start: '2024-10-18', end: '2024-10-18', name: '定时控制模式' },
        { start: '2024-12-10', end: '2024-12-12', name: '自动调光模式' }
      ],
      // 是否显示弹出层
      dialogVisible: false,
      showHolidays: false,
      showSchedules: true,
      ruleForm: {
        name: '',
        region: '',
        date1: '',
        delivery: false,
        type: [],
        resource: '',
        desc: ''
      }
    }
  },
  methods: {
    //新增计划安排
    addPlan() {
      this.dialogVisible = true;
    },
    holidayOverview() {
      this.showHolidays = true;
      this.showSchedules = false;
    },
    modeSetting() {
      this.showSchedules = true;
      this.showHolidays = false;
    },
    isHoliday(date) {
      if (!this.showHolidays) return false;
      const formattedDate = this.formatDate(date);
      return this.holidays.some(holiday => {
        const startDate = new Date(holiday.start);
        const endDate = new Date(holiday.end);
        return formattedDate >= this.formatDate(startDate) && formattedDate <= this.formatDate(endDate);
      });
    },
    getHolidayName(date) {
      const formattedDate = this.formatDate(date);
      for (const holiday of this.holidays) {
        const startDate = new Date(holiday.start);
        const endDate = new Date(holiday.end);
        if (formattedDate >= this.formatDate(startDate) && formattedDate <= this.formatDate(endDate)) {
          return holiday.name;
        }
      }
      return '';
    },
    isScheduled(date) {
      if (!this.showSchedules) return false;
      const formattedDate = this.formatDate(date);
      return this.schedules.some(schedule => {
        const startDate = new Date(schedule.start);
        const endDate = new Date(schedule.end);
        return formattedDate >= this.formatDate(startDate) && formattedDate <= this.formatDate(endDate);
      });
    },
    getScheduledName(date) {
      const formattedDate = this.formatDate(date);
      for (const schedule of this.schedules) {
        const startDate = new Date(schedule.start);
        const endDate = new Date(schedule.end);
        if (formattedDate >= this.formatDate(startDate) && formattedDate <= this.formatDate(endDate)) {
          return schedule.name;
        }
      }
      return '';
    },
    formatDate(date) {
      return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`;
    }
  }
}
</script>

<style scoped>
/* 日历样式         开始             ::v-deep */
.el-calendar {
  height: calc(100vh - 168px);
}

::v-deep .el-calendar-table .el-calendar-day:hover {
  background: #ecf5ff;
}

::v-deep .el-calendar-table td:hover {
  color: #409EFF;
  border: 1px solid #409EFF;
}

/* 日历样式         结束             */

.el-row {
  display: flex;
  flex-wrap: wrap;
  padding-bottom: 16px;
}

.el-row:last-child {
  padding-bottom: 0;
}

.el-col {
  border-radius: 4px;
  display: flex;
  flex-direction: column;
  align-items: stretch;
}

.bg-purple {
  background-color: #FFFFFF;
}

.grid-content {
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  border: 1px solid #FFFFFF;
  min-height: 100%;
  color: #606266;
  cursor: pointer;
  background-color: #FFFFFF;
  font-family: '微软雅黑';
  font-size: 14px;
}
</style>
