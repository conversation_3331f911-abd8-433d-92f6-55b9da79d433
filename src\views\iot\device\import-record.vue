<template>
    <el-dialog :close-on-click-modal="false" title="设备导入记录" :visible.sync="open" width="800px">
        <div style="margin-top:-55px;">
            <el-divider style="margin-top:-30px;"></el-divider>
            <el-form :model="queryParams" ref="queryForm" :inline="true" label-width="68px">
                <el-form-item prop="productId">
                    <el-select v-model="queryParams.productId" placeholder="产品名称" style="width: 180px" filterable>
                        <el-option v-for="item in productList" :key="item.value" :label="item.label"
                            :value="item.value">
                        </el-option>
                    </el-select>
                </el-form-item>
                <el-form-item prop="status">
                    <el-select v-model="queryParams.status" placeholder="批次任务状态" style="width: 180px" filterable>
                        <el-option v-for="item in statusList" :key="item.value" :label="item.label" :value="item.value">
                        </el-option>
                    </el-select>
                </el-form-item>
                <el-form-item>
                    <el-date-picker v-model="daterangeTime" size="small" style="width: 180px" value-format="yyyy-MM-dd"
                        type="daterange" range-separator="-" start-placeholder="开始日期"
                        end-placeholder="结束日期"></el-date-picker>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
                    <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
                </el-form-item>
            </el-form>

            <el-table v-loading="loading" ref="singleTable" :data="dataList" size="mini">
                <el-table-column label="批次号" align="left" />
                <el-table-column label="设备总数" align="center" />
                <el-table-column label="成功数量" align="center" />
                <el-table-column label="失败数量" align="center" />
                <el-table-column label="批次任务数量" align="center" />
                <el-table-column label="完成时间" align="center" />
                <el-table-column label="创建时间" align="center" />
            </el-table>
            <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum"
                :limit.sync="queryParams.pageSize" />
            <!-- @pagination="getList"  -->
        </div>
    </el-dialog>
</template>

<script>
import {
    listProduct
} from "@/api/iot/product";
import {
    listImportRecord
} from "@/api/iot/device";


export default {
    name: "importRecord",
    dicts: [],
    data() {
        return {
            // 遮罩层
            loading: true,
            // 总条数
            total: 0,
            // 打开选择产品对话框
            open: false,
            // 产品列表
            productList: [],
            statusList: [],
            dataList: [],
            //时间范围
            daterangeTime: [],
            // 查询参数
            queryParams: {
                pageNum: 1,
                pageSize: 10,
                productName: null,
            },
        };
    },
    created() {
        this.getProductList();
    },
    methods: {
        /** 查询产品列表 */
        getProductList() {
            this.loading = true;
            const params = {
                pageSize: 999,
            }
            listProduct(params).then(response => {
                this.productList = response.rows.map((item) => {
                    return { value: item.productId, label: item.productName };
                });
                this.loading = false;
            });
        },
        //查询导入记录列表
        getList() {
            this.loading = true;
            listImportRecord().then(response => {
                this.dataList = response.rows;
                this.toltal = response.total;
                this.loading = false;
            });
        },
        /** 搜索按钮操作 */
        handleQuery() {
            this.queryParams.pageNum = 1;
            this.getList();
        },
        /** 重置按钮操作 */
        resetQuery() {
            this.resetForm("queryForm");
            this.handleQuery();
        },
        /**关闭对话框 */
        closeDialog() {
            this.open = false;
        }
    }
};
</script>
