# 辉采科技物联网云平台

## 项目架构

这是一个基于 Vue.js 的物联网云平台前端项目，采用了 Vue 2.6、Element UI 和多种技术栈构建。

### 技术栈

-   **前端框架**: Vue.js 2.6.12
-   **UI 框架**: Element UI 2.15.10
-   **状态管理**: Vuex 3.6.2
-   **路由管理**: Vue Router 3.4.9
-   **HTTP 客户端**: Axios
-   **可视化**: ECharts 4.9.0, DataV
-   **地图服务**: 百度地图
-   **实时通信**: MQTT
-   **代码编辑器**: Monaco Editor, CodeMirror
-   **其他库**: Three.js (3D 渲染), vue-video-player (视频播放), mxgraph (图形编辑)

### 项目结构

```
├── public                 # 静态资源
├── src                    # 源代码
│   ├── api                # API接口
│   │   ├── iot            # 物联网相关API
│   │   ├── system         # 系统相关API
│   │   ├── monitor        # 监控相关API
│   │   ├── scada          # SCADA相关API
│   │   └── ...
│   ├── assets             # 主题、字体等静态资源
│   ├── components         # 全局公用组件
│   ├── directive          # 全局指令
│   ├── layout             # 全局布局
│   ├── plugins            # 插件
│   ├── router             # 路由
│   ├── store              # 全局状态管理
│   ├── utils              # 全局工具方法
│   ├── views              # 所有页面
│   │   ├── iot            # 物联网功能模块
│   │   ├── system         # 系统管理模块
│   │   ├── monitor        # 系统监控模块
│   │   ├── scada          # SCADA界面
│   │   ├── bigScreen      # 大屏展示
│   │   └── ...
│   ├── App.vue            # 入口页面
│   ├── main.js            # 入口文件
│   └── permission.js      # 权限控制
├── .env.development       # 开发环境配置
├── .env.production        # 生产环境配置
├── .env.staging           # 测试环境配置
└── vue.config.js          # Vue CLI配置
```

### 功能模块划分

#### 1. 物联网核心功能 (IoT)

-   设备管理: 设备注册、状态监控、远程控制
-   产品管理: 产品定义、模型管理
-   设备分组与场景管理
-   固件管理: 远程升级
-   告警管理: 告警规则、告警记录
-   日志管理: 设备日志、事件日志

#### 2. 系统管理 (System)

-   用户管理
-   角色权限管理
-   菜单管理
-   部门管理
-   系统配置

#### 3. 可视化与监控

-   仪表盘: 数据可视化
-   大屏展示: 数据大屏
-   SCADA 系统: 图形化监控界面
-   拓扑图: 设备连接关系图

#### 4. 连接与协议

-   MQTT 通信
-   SIP 协议支持
-   自定义协议管理

## 开发环境

```bash
# 克隆项目
git clone https://gitee.com/y_project/RuoYi-Vue

# 进入项目目录
cd ruoyi-ui

# 安装依赖
npm install

# 建议不要直接使用 cnpm 安装依赖，会有各种诡异的 bug。可以通过如下操作解决 npm 下载速度慢的问题
npm install --registry=https://registry.npmmirror.com

# 启动服务
npm run dev
```

浏览器访问 http://localhost:80

## 发布

```bash
# 构建测试环境
npm run build:stage

# 构建生产环境
npm run build:prod
```
