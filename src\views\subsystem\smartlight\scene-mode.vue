<template>
  <div style="height: calc(100vh - 50px); padding: 16px; background-color: #0C1F33;">
    <div style="width: 100%; height: 100%;">
      <el-row :gutter="16">
        <el-col :span="6">
          <div class="grid-content">
            <dv-border-box-12 >
              <div style="width: 100%; height: 72%; display: flex; flex-direction: row;">
                <div style="width: 60%; height: 100%;">
                  <div style="width: 100%; height: 40%; color: #FFFFFF; font-size: 1vw; display: flex; justify-content: center; align-items: center;">
                    <span>阅读模式</span>
                  </div>
                  <div style="width: 100%; height: 60%;">
                    <!-- <span>运行时间</span>
                    <span>节能模式</span>
                    <span>节能模式</span> -->
                  </div>
                </div>
                <div style="width: 40%; height: 100%; display: flex; justify-content: center; align-items: center;">
                  <svg style="width: 50%; height: 50%;" t="1730194418153" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="19423" width="200" height="200">
                    <path d="M651.636 279.273H372.364V46.545h279.272v232.728zM418.91 232.727h186.182V93.091H418.909v139.636zM512 882.967a306.292 306.292 0 0 1-217.228-89.832 298.938 298.938 0 0 1-57.018-349.603 383.185 383.185 0 0 1 83.666-98.048c7.26-6.773 13.963-12.94 19.27-18.34a237.033 237.033 0 0 0 39.82-63.255l42.867 18.176a286.93 286.93 0 0 1-49.78 77.987c-5.725 5.725-12.73 12.265-20.41 19.456a353.047 353.047 0 0 0-73.472 84.224 253.487 253.487 0 0 0 47.965 296.495 260.957 260.957 0 0 0 368.64 0 253.487 253.487 0 0 0 47.988-296.518 353.21 353.21 0 0 0-73.472-84.2c-7.68-7.192-14.685-13.732-20.41-19.457a286.93 286.93 0 0 1-49.78-77.987l42.845-18.199a242.781 242.781 0 0 0 39.843 63.255c5.422 5.4 12.032 11.637 19.27 18.34a383.325 383.325 0 0 1 83.665 98.024 298.961 298.961 0 0 1-57.018 349.626A306.292 306.292 0 0 1 512 882.967z m69.818-836.422H442.182V0h139.636v46.545z m-23.273 614.726L512 614.726l-46.545 46.545-102.726-102.726 79.453-79.453V256h46.545v242.362l-60.183 60.183 36.91 36.911L512 548.911l46.545 46.545 36.911-36.91-60.183-60.184V256h46.545v223.092l79.453 79.453z m249.182-318.999l-32.908-32.908 69.818-69.818 32.908 32.908z m-591.454 0l-69.818-69.818 32.908-32.908 69.818 69.818zM821.364 924.09l-69.818-69.818 32.908-32.908 69.818 69.819z m-618.728 0l-32.908-32.907 69.818-69.819 32.908 32.908z m774.819-319h-93.091v-46.545h93.09v46.546z m-837.819 0h-93.09v-46.545h93.09v46.546zM535.273 1024h-46.546v-93.09h46.546V1024z" :fill="lightColor1" :opacity="lightLevel1 / 100" p-id="19424"></path></svg>
                </div>
              </div>
              <div style="width: 100%; height: 28%; padding: 0 4%;">
                <div class="slider-demo-block">
                  <span class="demonstration">强度</span>
                  <el-slider v-model="lightLevel1"/>
                </div>
              </div>
            </dv-border-box-12>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="grid-content">
            <dv-border-box-12 >
              <div style="width: 100%; height: 72%; display: flex; flex-direction: row;">
                <div style="width: 60%; height: 100%;">
                  <div style="width: 100%; height: 40%; color: #FFFFFF; font-size: 1vw; display: flex; justify-content: center; align-items: center;">
                    <span>欢迎模式</span>
                  </div>
                  <div style="width: 100%; height: 60%;">
                    <!-- <span>运行时间</span>
                    <span>节能模式</span>
                    <span>节能模式</span> -->
                  </div>
                </div>
                <div style="width: 40%; height: 100%; display: flex; justify-content: center; align-items: center;">
                  <svg style="width: 50%; height: 50%;" t="1730194418153" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="19423" width="200" height="200">
                    <path d="M651.636 279.273H372.364V46.545h279.272v232.728zM418.91 232.727h186.182V93.091H418.909v139.636zM512 882.967a306.292 306.292 0 0 1-217.228-89.832 298.938 298.938 0 0 1-57.018-349.603 383.185 383.185 0 0 1 83.666-98.048c7.26-6.773 13.963-12.94 19.27-18.34a237.033 237.033 0 0 0 39.82-63.255l42.867 18.176a286.93 286.93 0 0 1-49.78 77.987c-5.725 5.725-12.73 12.265-20.41 19.456a353.047 353.047 0 0 0-73.472 84.224 253.487 253.487 0 0 0 47.965 296.495 260.957 260.957 0 0 0 368.64 0 253.487 253.487 0 0 0 47.988-296.518 353.21 353.21 0 0 0-73.472-84.2c-7.68-7.192-14.685-13.732-20.41-19.457a286.93 286.93 0 0 1-49.78-77.987l42.845-18.199a242.781 242.781 0 0 0 39.843 63.255c5.422 5.4 12.032 11.637 19.27 18.34a383.325 383.325 0 0 1 83.665 98.024 298.961 298.961 0 0 1-57.018 349.626A306.292 306.292 0 0 1 512 882.967z m69.818-836.422H442.182V0h139.636v46.545z m-23.273 614.726L512 614.726l-46.545 46.545-102.726-102.726 79.453-79.453V256h46.545v242.362l-60.183 60.183 36.91 36.911L512 548.911l46.545 46.545 36.911-36.91-60.183-60.184V256h46.545v223.092l79.453 79.453z m249.182-318.999l-32.908-32.908 69.818-69.818 32.908 32.908z m-591.454 0l-69.818-69.818 32.908-32.908 69.818 69.818zM821.364 924.09l-69.818-69.818 32.908-32.908 69.818 69.819z m-618.728 0l-32.908-32.907 69.818-69.819 32.908 32.908z m774.819-319h-93.091v-46.545h93.09v46.546z m-837.819 0h-93.09v-46.545h93.09v46.546zM535.273 1024h-46.546v-93.09h46.546V1024z" :fill="lightColor2" :opacity="lightLevel2 / 100" p-id="19424"></path></svg>
                </div>
              </div>
              <div style="width: 100%; height: 28%; padding: 0 4%;">
                <div class="slider-demo-block">
                  <span class="demonstration">强度</span>
                  <el-slider v-model="lightLevel2"/>
                </div>
              </div>
            </dv-border-box-12 >
          </div>
        </el-col>
        <el-col :span="6">
          <div class="grid-content">
            <dv-border-box-12 >
              <div style="width: 100%; height: 72%; display: flex; flex-direction: row;">
                <div style="width: 60%; height: 100%;">
                  <div style="width: 100%; height: 40%; color: #FFFFFF; font-size: 1vw; display: flex; justify-content: center; align-items: center;">
                    <span>节能模式</span>
                  </div>
                  <div style="width: 100%; height: 60%;">
                    <!-- <span>运行时间</span>
                    <span>节能模式</span>
                    <span>节能模式</span> -->
                  </div>
                </div>
                <div style="width: 40%; height: 100%; display: flex; justify-content: center; align-items: center;">
                  <svg style="width: 50%; height: 50%;" t="1730194418153" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="19423" width="200" height="200">
                    <path d="M651.636 279.273H372.364V46.545h279.272v232.728zM418.91 232.727h186.182V93.091H418.909v139.636zM512 882.967a306.292 306.292 0 0 1-217.228-89.832 298.938 298.938 0 0 1-57.018-349.603 383.185 383.185 0 0 1 83.666-98.048c7.26-6.773 13.963-12.94 19.27-18.34a237.033 237.033 0 0 0 39.82-63.255l42.867 18.176a286.93 286.93 0 0 1-49.78 77.987c-5.725 5.725-12.73 12.265-20.41 19.456a353.047 353.047 0 0 0-73.472 84.224 253.487 253.487 0 0 0 47.965 296.495 260.957 260.957 0 0 0 368.64 0 253.487 253.487 0 0 0 47.988-296.518 353.21 353.21 0 0 0-73.472-84.2c-7.68-7.192-14.685-13.732-20.41-19.457a286.93 286.93 0 0 1-49.78-77.987l42.845-18.199a242.781 242.781 0 0 0 39.843 63.255c5.422 5.4 12.032 11.637 19.27 18.34a383.325 383.325 0 0 1 83.665 98.024 298.961 298.961 0 0 1-57.018 349.626A306.292 306.292 0 0 1 512 882.967z m69.818-836.422H442.182V0h139.636v46.545z m-23.273 614.726L512 614.726l-46.545 46.545-102.726-102.726 79.453-79.453V256h46.545v242.362l-60.183 60.183 36.91 36.911L512 548.911l46.545 46.545 36.911-36.91-60.183-60.184V256h46.545v223.092l79.453 79.453z m249.182-318.999l-32.908-32.908 69.818-69.818 32.908 32.908z m-591.454 0l-69.818-69.818 32.908-32.908 69.818 69.818zM821.364 924.09l-69.818-69.818 32.908-32.908 69.818 69.819z m-618.728 0l-32.908-32.907 69.818-69.819 32.908 32.908z m774.819-319h-93.091v-46.545h93.09v46.546z m-837.819 0h-93.09v-46.545h93.09v46.546zM535.273 1024h-46.546v-93.09h46.546V1024z" :fill="lightColor3" :opacity="lightLevel3 / 100" p-id="19424"></path></svg>
                </div>
              </div>
              <div style="width: 100%; height: 28%; padding: 0 4%;">
                <div class="slider-demo-block">
                  <span class="demonstration">强度</span>
                  <el-slider v-model="lightLevel3"/>
                </div>
              </div>
            </dv-border-box-12 >
          </div>
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      lightLevel1: 100, // 阅读模式
      lightColor1: '#FFFFFF', 
      lightLevel2: 70,  // 欢迎模式
      lightColor2: '#FFD700',
      lightLevel3: 30,  // 节能模式
      lightColor3: '#F5F5F5'
    }
  },
  methods: {
    formatTooltip(val) {
      return val / 100;
    }
  }
}
</script>

<style scoped>
.slider-demo-block {
  height: 100%;
  width: 100%;
  display: flex;
  align-items: center;
  font-size: 1vw;
  color: aliceblue;
}

.slider-demo-block .demonstration {
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  line-height: 44px;
  margin-bottom: 0;
}

.slider-demo-block .demonstration + .el-slider {
  flex: 0 0 85%;
}

.el-row {
  display: flex;
  flex-wrap: wrap;
  /* margin-bottom: 16px; */
}

.el-col {
  display: flex;
  flex-direction: column;
  align-items: stretch;
  padding-bottom: 16px;
}

.el-col:last-child {
  /* margin-bottom: 0; */
  padding-bottom: 0;
}

.grid-content {
  /* background-image: url('../../../assets/report-form/light/preview.png');
  background-size: 100% 100%; */
  background-color: #0C1A2D;
  height: 24vh;
}

.grid-content span {
  font-size: 0.84vw;
  color: #F5F7FA;
}
</style>