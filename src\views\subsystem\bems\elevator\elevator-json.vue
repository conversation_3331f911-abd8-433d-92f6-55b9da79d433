<template>
  <div id="app">
    <div class="container"
         :style="{
           'background-image': `url(${jsonData.layer.backgroundImage})`,
           width: `${jsonData.layer.width}px`,
           height: `${jsonData.layer.height}px`
         }">

      <!-- 动态加载图像 -->
      <img v-for="image in images" :key="image.identifier"
           :src="image.style.url"
           :style="{
             top: `${image.style.position.y}px`,
             left: `${image.style.position.x}px`,
             width: `${image.style.position.w}px`,
             height: `${image.style.position.h}px`,
             transform: image.style.transformType,
             visibility: image.style.visible ? 'visible' : 'hidden'
           }">
      
      <!-- 显示文本 -->
      <div v-for="text in texts" :key="text.identifier" class="text"
           :style="{
             top: `${text.style.position.y}px`,
             left: `${text.style.position.x}px`,
             width: `${text.style.position.w}px`,
             height: `${text.style.position.h}px`,
             lineHeight: `${text.style.position.h}px`,
             color: text.style.foreColor,
             backgroundColor: text.style.backColor,
             borderRadius: `${text.style.borderRadius}px`,
             foreColor: text.style.foreColor,
             textAlign: text.style.textAlign,
             fontSize: `${text.style.fontSize}px`,
             fontFamily: text.style.fontFamily,
             visibility: text.style.visible ? 'visible' : 'hidden'
            //  'border': '1px dashed red' // 添加调试边框
           }">
        {{ text.style.text }}
      </div>

      <!-- 显示计时器 -->
      <div v-if="showTimer" class="timer"
           :style="{
             top: `${timer.style.position.y}px`,
             left: `${timer.style.position.x}px`,
             width: `${timer.style.position.w}px`,
             height: `${timer.style.position.h}px`
           }">
        {{ timer.name }}
      </div>
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      jsonData: {
        "name": "--",
        "layer": {
          "backColor": "",
          "backgroundImage": "/dev-api/profile/upload/2024/10/11/1_20241011104751A001.png",
          "widthHeightRatio": "",
          "width": 960,
          "height": 600
        },
        "components": [{
          "type": "text",
          "componentShow": ["单击", "组件颜色", "动画"],
          "action": [],
          "dataBind": {
            "djAction": false,
            "action": "",
            "productId": "",
            "serialNumber": "",
            "identifier": "",
            "modelName": "",
            "modelValue": "",
            "redirectUrl": ""
          },
          "dataAction": {
            "xyAction": false,
            "xzAction": false,
            "ssAction": false,
            "hdAction": false,
            "serialNumber": "",
            "identifier": "",
            "modelName": "",
            "paramJudge": "",
            "paramJudgeData": "",
            "rotationSpeed": "中",
            "translateList": []
          },
          "style": {
            "position": {
              "x": 750,
              "y": 282,
              "w": 120,
              "h": 36
            },
            "backColor": "rgba(30, 144, 255, 1)",
            "foreColor": "rgba(255, 255, 255, 1)",
            "zIndex": 1,
            "transform": 0,
            "transformType": "rotate(0deg)",
            "waterBorderWidth": 0,
            "waterBorderColor": "rgba(30, 144, 255, 1)",
            "text": "待梯",
            "textAlign": "center",
            "fontSize": 16,
            "fontFamily": "Arial",
            "visible": true,
            "borderWidth": 0,
            "borderStyle": "solid",
            "borderColor": "#ccccccff",
            "borderRadius": 40,
            "temp": {
              "position": {
                "x": 750,
                "y": 282
              }
            }
          },
          "identifier": "12af6b11-c1e8-cb4f-cc92-2b3e0ac69da8",
          "name": "text41"
        }, {
          "type": "image",
          "componentShow": ["动画", "单击", "组件颜色", "滤镜渲染", "组件填充", "参数绑定"],
          "action": [],
          "hdClassName": "",
          "dataBind": {
            "action": "",
            "productId": "",
            "serialNumber": "",
            "identifier": "",
            "modelName": "",
            "modelValue": "",
            "redirectUrl": "",
            "stateList": []
          },
          "dataAction": {
            "serialNumber": "",
            "identifier": "",
            "modelName": "",
            "paramJudge": "",
            "paramJudgeData": "",
            "rotationSpeed": "中",
            "translateList": []
          },
          "style": {
            "position": {
              "x": 750,
              "y": 205,
              "w": 120,
              "h": 50
            },
            "backColor": "rgba(255, 255, 255, 0)",
            "foreColor": "",
            "zIndex": 1,
            "transform": 0,
            "url": "/dev-api/profile/avatar/anniu (83).svg",
            "transformType": "rotate(0deg)",
            "isFilter": true,
            "visible": true,
            "borderWidth": 0,
            "borderStyle": "solid",
            "borderColor": "#ccccccff",
            "temp": {
              "position": {
                "x": 750,
                "y": 205
              }
            }
          },
          "identifier": "c9355c4a-0a8c-b5cb-7c25-b23fe0a8721c",
          "name": "image1"
        }, {
          "type": "image",
          "componentShow": ["动画", "单击", "组件颜色", "滤镜渲染", "组件填充", "参数绑定"],
          "action": [],
          "hdClassName": "",
          "dataBind": {
            "action": "",
            "productId": "",
            "serialNumber": "",
            "identifier": "",
            "modelName": "",
            "modelValue": "",
            "redirectUrl": "",
            "stateList": []
          },
          "dataAction": {
            "serialNumber": "",
            "identifier": "",
            "modelName": "",
            "paramJudge": "",
            "paramJudgeData": "",
            "rotationSpeed": "中",
            "translateList": []
          },
          "style": {
            "position": {
              "x": 750,
              "y": 345,
              "w": 120,
              "h": 50
            },
            "backColor": "rgba(255, 255, 255, 0)",
            "foreColor": "",
            "zIndex": 1,
            "transform": 0,
            "url": "/dev-api/profile/avatar/anniu (77).svg",
            "transformType": "rotate(0deg)",
            "isFilter": true,
            "visible": true,
            "borderWidth": 0,
            "borderStyle": "solid",
            "borderColor": "#ccccccff",
            "temp": {
              "position": {
                "x": 750,
                "y": 445
              }
            }
          },
          "identifier": "73b21d98-b979-36fb-efbc-258f863aec0f",
          "name": "image2"
        }, {
          "type": "text",
          "componentShow": ["单击", "组件颜色", "动画"],
          "action": [],
          "dataBind": {
            "djAction": false,
            "action": "",
            "productId": "",
            "serialNumber": "",
            "identifier": "",
            "modelName": "",
            "modelValue": "",
            "redirectUrl": ""
          },
          "dataAction": {
            "xyAction": false,
            "xzAction": false,
            "ssAction": false,
            "hdAction": false,
            "serialNumber": "",
            "identifier": "",
            "modelName": "",
            "paramJudge": "",
            "paramJudgeData": "",
            "rotationSpeed": "中",
            "translateList": []
          },
          "style": {
            "position": {
              "x": 750,
              "y": 210,
              "w": 120,
              "h": 40
            },
            "backColor": "#ff000000",
            "foreColor": "rgba(255, 255, 255, 1)",
            "zIndex": 1,
            "transform": 0,
            "transformType": "rotate(0deg)",
            "waterBorderWidth": 1,
            "waterBorderColor": "rgba(255, 255, 255, 0)",
            "text": "上行",
            "textAlign": "center",
            "fontSize": 14,
            "fontFamily": "Arial",
            "visible": true,
            "borderWidth": 0,
            "borderStyle": "solid",
            "borderColor": "#ccccccff",
            "temp": {
              "position": {
                "x": 750,
                "y": 210
              }
            }
          },
          "identifier": "3dc0452a-2a40-88cd-3e3d-0dd1fcc38955",
          "name": "text3"
        }, {
          "type": "text",
          "componentShow": ["单击", "组件颜色", "动画"],
          "action": [],
          "dataBind": {
            "djAction": false,
            "action": "",
            "productId": "",
            "serialNumber": "",
            "identifier": "",
            "modelName": "",
            "modelValue": "",
            "redirectUrl": ""
          },
          "dataAction": {
            "xyAction": false,
            "xzAction": false,
            "ssAction": false,
            "hdAction": false,
            "serialNumber": "",
            "identifier": "",
            "modelName": "",
            "paramJudge": "",
            "paramJudgeData": "",
            "rotationSpeed": "中",
            "translateList": []
          },
          "style": {
            "position": {
              "x": 750,
              "y": 350,
              "w": 120,
              "h": 40
            },
            "backColor": "#ff000000",
            "foreColor": "rgba(255, 255, 255, 1)",
            "zIndex": 1,
            "transform": 0,
            "transformType": "rotate(0deg)",
            "waterBorderWidth": 1,
            "waterBorderColor": "rgba(255, 255, 255, 0)",
            "text": "下行",
            "textAlign": "center",
            "fontSize": 14,
            "fontFamily": "Arial",
            "visible": true,
            "borderWidth": 0,
            "borderStyle": "solid",
            "borderColor": "#ccccccff",
            "temp": {
              "position": {
                "x": 750,
                "y": 450
              }
            }
          },
          "identifier": "5dbc5d11-5959-266b-fd5b-ca58a9f1814e",
          "name": "text4",
          "identifiers": ["5dbc5d11-5959-266b-fd5b-ca58a9f1814e"]
        }, {
          "type": "text",
          "componentShow": ["单击", "组件颜色", "动画"],
          "action": [],
          "dataBind": {
            "djAction": false,
            "action": "",
            "productId": "",
            "serialNumber": "",
            "identifier": "",
            "modelName": "",
            "modelValue": "",
            "redirectUrl": ""
          },
          "dataAction": {
            "xyAction": false,
            "xzAction": false,
            "ssAction": false,
            "hdAction": false,
            "serialNumber": "",
            "identifier": "",
            "modelName": "",
            "paramJudge": "",
            "paramJudgeData": "",
            "rotationSpeed": "中",
            "translateList": []
          },
          "style": {
            "position": {
              "x": 430,
              "y": 40,
              "w": 100,
              "h": 25
            },
            "backColor": "#ff000000",
            "foreColor": "rgba(255, 255, 255, 1)",
            "zIndex": 1,
            "transform": 0,
            "transformType": "rotate(0deg)",
            "waterBorderWidth": 1,
            "waterBorderColor": "rgba(255, 255, 255, 0)",
            "text": "1",
            "textAlign": "center",
            "fontSize": 14,
            "fontFamily": "Arial",
            "visible": true,
            "borderWidth": 0,
            "borderStyle": "solid",
            "borderColor": "#ccccccff",
            "temp": {
              "position": {
                "x": 430,
                "y": 62
              }
            }
          },
          "identifier": "64ba702d-74f4-215c-5b46-71cb11cbca01",
          "name": "text5"
        }, {
          "type": "image",
          "componentShow": ["动画", "单击", "组件颜色", "滤镜渲染", "组件填充", "参数绑定"],
          "action": [],
          "hdClassName": "",
          "dataBind": {
            "action": "",
            "productId": "",
            "serialNumber": "",
            "identifier": "",
            "modelName": "",
            "modelValue": "",
            "redirectUrl": "",
            "stateList": []
          },
          "dataAction": {
            "serialNumber": "",
            "identifier": "",
            "modelName": "",
            "paramJudge": "",
            "paramJudgeData": "",
            "rotationSpeed": "中",
            "translateList": []
          },
          "style": {
            "position": {
              "x": 750,
              "y": 50,
              "w": 50,
              "h": 50
            },
            "backColor": "rgba(255, 255, 255, 0)",
            "foreColor": "",
            "zIndex": 1,
            "transform": 0,
            "url": "/dev-api/profile/avatar/指示灯 (38).svg",
            "transformType": "rotate(0deg)",
            "isFilter": true,
            "visible": true,
            "borderWidth": 0,
            "borderStyle": "solid",
            "borderColor": "#ccccccff",
            "temp": {
              "position": {
                "x": 740,
                "y": 50
              }
            }
          },
          "identifier": "98c4a6dc-0cde-078b-816c-3a301c2f0e2d",
          "name": "image6"
        }, {
          "type": "image",
          "componentShow": ["动画", "单击", "组件颜色", "滤镜渲染", "组件填充", "参数绑定"],
          "action": [],
          "hdClassName": "",
          "dataBind": {
            "action": "",
            "productId": "",
            "serialNumber": "",
            "identifier": "",
            "modelName": "",
            "modelValue": "",
            "redirectUrl": "",
            "stateList": []
          },
          "dataAction": {
            "serialNumber": "",
            "identifier": "",
            "modelName": "",
            "paramJudge": "",
            "paramJudgeData": "",
            "rotationSpeed": "中",
            "translateList": []
          },
          "style": {
            "position": {
              "x": 820,
              "y": 50,
              "w": 50,
              "h": 50
            },
            "backColor": "rgba(255, 255, 255, 0)",
            "foreColor": "",
            "zIndex": 1,
            "transform": 0,
            "url": "/dev-api/profile/avatar/指示灯 (42).svg",
            "transformType": "rotate(0deg)",
            "isFilter": true,
            "visible": true,
            "borderWidth": 0,
            "borderStyle": "solid",
            "borderColor": "#ccccccff",
            "temp": {
              "position": {
                "x": 800,
                "y": 50
              }
            }
          },
          "identifier": "6604c9a7-77ee-9fd3-53d8-ac867f98c572",
          "name": "image7"
        }]
      },
      images: [],
      texts: [],
      showTimer: false,
      timer: {}
    };
  },
  mounted() {
    this.images = this.jsonData.components.filter(comp => comp.type === 'image');
    this.texts = this.jsonData.components.filter(comp => comp.type === 'text');
    this.timer = this.jsonData.components.find(comp => comp.type === 'timer');
    if (this.timer) {
      this.showTimer = true;
    }
  }
};
</script>

<style scoped>
.container {
  position: relative;
  background-size: cover;
  background-position: center;
  overflow: auto;
}

img {
  position: absolute;
  top: 0;
  left: 0;
  transform-origin: top left;
  transition: all 0.3s ease;
}

.text {
  position: absolute;
}

.timer {
  position: absolute;
  top: 0;
  left: 0;
  color: #00CED1;
  text-align: center;
  font-size: 30px;
  font-family: Arial;
}
</style>