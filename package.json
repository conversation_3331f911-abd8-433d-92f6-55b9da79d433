{"name": "fastbee", "version": "3.8.5", "description": "辉采科技物联网云平台", "author": "k<PERSON><PERSON><PERSON><PERSON>", "license": "AGPL3.0", "scripts": {"dev": "set NODE_OPTIONS=--openssl-legacy-provider & vue-cli-service serve", "build:prod": "set NODE_OPTIONS=--openssl-legacy-provider node your-app.js & vue-cli-service build", "build:stage": "vue-cli-service build --mode staging", "preview": "node build/index.js --preview", "lint": "eslint --ext .js,.vue src"}, "husky": {"hooks": {"pre-commit": "lint-staged"}}, "lint-staged": {"src/**/*.{js,vue}": ["eslint --fix", "git add"]}, "keywords": ["vue", "admin", "dashboard", "element-ui", "boilerplate", "admin-template", "management-system"], "repository": {"type": "git", "url": "https://gitee.com/y_project/RuoYi-Vue.git"}, "dependencies": {"16": "^0.0.2", "@easydarwin/easywasmplayer": "^4.0.7", "@jiaminghi/data-view": "^2.10.0", "@riophae/vue-treeselect": "0.4.0", "@vue/composition-api": "^1.7.2", "animejs": "^3.2.1", "axios": "^0.24.0", "clipboard": "2.0.8", "codemirror": "^5.65.15", "core-js": "3.25.3", "echarts": "^4.9.0", "echarts-liquidfill": "^2.0.6", "element-china-area-data": "^4.1.1", "element-ui": "2.15.10", "ezuikit-js": "^7.7.10", "file-saver": "2.0.5", "flv.js": "^1.6.2", "fuse.js": "6.4.3", "highlight.js": "9.18.5", "html2canvas": "^1.4.1", "js-beautify": "1.13.0", "js-cookie": "3.0.1", "jsencrypt": "3.0.0-rc.1", "jshint": "^2.13.4", "json-loader": "^0.5.7", "jsonlint": "^1.6.3", "lodash": "^4.17.21", "minimatch": "^3.1.2", "moment": "^2.29.4", "monaco-editor": "0.27.0", "monaco-editor-webpack-plugin": "4.2.0", "mqtt": "^4.3.3", "mxgraph": "^4.2.2", "mxgraph-js": "^1.0.1", "nprogress": "0.2.0", "photo-sphere-viewer": "^4.8.1", "quasar": "^2.14.0", "quill": "1.3.7", "screenfull": "5.0.2", "script-loader": "^0.7.2", "sortablejs": "1.10.2", "speak-tts": "^2.0.8", "sql-formatter": "^4.0.2", "three": "^0.142.0", "vue": "2.6.12", "vue-3d-model": "^1.4.1", "vue-axios": "^3.5.2", "vue-baidu-map": "^0.21.22", "vue-clipboard2": "^0.3.3", "vue-codemirror": "^4.0.6", "vue-contextmenujs": "^1.4.11", "vue-count-to": "1.0.13", "vue-cropper": "0.5.5", "vue-easytable": "^2.14.0", "vue-json-viewer": "^2.2.21", "vue-meta": "2.4.0", "vue-qr": "^4.0.9", "vue-router": "3.4.9", "vue-ruler-tool": "^1.2.4", "vue-seamless-scroll": "^1.1.23", "vue-tianditu": "^2.7.6", "vue-video-player": "^5.0.2", "vue2-ace-editor": "^0.0.15", "vuedraggable": "2.24.3", "vuex": "^3.6.2"}, "devDependencies": {"@vue/cli-plugin-babel": "4.4.6", "@vue/cli-plugin-eslint": "4.4.6", "@vue/cli-service": "4.4.6", "babel-eslint": "10.1.0", "babel-plugin-dynamic-import-node": "^2.3.3", "chalk": "4.1.0", "compression-webpack-plugin": "5.0.2", "connect": "3.6.6", "eslint": "^7.28.0", "eslint-plugin-vue": "7.2.0", "lint-staged": "10.5.3", "runjs": "4.4.2", "sass": "1.32.13", "sass-loader": "10.1.1", "script-ext-html-webpack-plugin": "2.1.5", "svg-sprite-loader": "5.1.1", "vue-template-compiler": "2.6.12", "vue2-ace-editor": "^0.0.15"}, "engines": {"node": ">=8.9", "npm": ">= 3.0.0"}, "browserslist": ["> 1%", "last 2 versions"]}