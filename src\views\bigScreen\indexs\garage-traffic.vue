<style scoped>
.contents_garage {
    width: 20vw;
    height: 24.4vh;
}
.contents_garage_occupy {
    width: 10vw;
    height: 24.4vh;
    float: left;
}
.contents_garage_surplus {
    width: 10vw;
    height: 24.4vh;
    float: left;
}
.contents_garage_icon {
    width: 10vw;
    height: 5.6vh;
}
.contents_garage_icon svg {
    width: 2.5vw;
    height: 5.6vh;
}
.contents_garage_tails {
    width: 10vw;
    height: 18.4vh;
    margin-top: 0.4vh;
}
.contents_garage_tails p {
    width: 10vw;
    height: 4vh;
    line-height: 4vh;
    color: #ffffff;
    font-size: 0.66vw;
    font-weight: 600;
    text-indent: 0.8vw;
}
.contents_garage_tails p span {
    font-size: 1.2vw;
    font-weight: 600;
    color: #1296db;
    margin: 0 0 0 0.8vw;
}
</style>

<template>
    <div class="contents_garage">
        <div class="contents_garage_occupy">
            <dv-water-level-pond :config="configCpu" style="width: 7vw; height: 7vw; margin: 5vh 0 0 1.5vw" />
        </div>
        <div class="contents_garage_surplus">
            <!-- <div class="contents_garage_icon" >
                <svg t="1713422595102" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="18540" width="200" height="200"><path d="M959.381795 213.313294c0-82.421174-66.815755-149.236929-149.236929-149.236929L213.225801 64.076364c-82.421174 0-149.236929 66.815755-149.236929 149.236929l0 596.919065c0 82.421174 66.815755 149.236929 149.236929 149.236929L810.144866 959.469288c82.421174 0 149.236929-66.815755 149.236929-149.236929L959.381795 213.313294zM884.680443 784.03879c0 56.196906-45.556569 101.753475-101.752452 101.753475L239.41937 885.792265c-56.195883 0-101.752452-45.556569-101.752452-101.753475L137.666918 240.531192c0-56.196906 45.556569-101.753475 101.752452-101.753475l543.509644 0c56.195883 0 101.752452 45.556569 101.752452 101.753475L884.681466 784.03879zM592.384283 401.935232c0-76.622098-63.086827-138.735761-140.912333-138.735761-3.542686 0-7.016811 0.26606-10.499122 0.522909L316.493769 263.72238l0 0.261966-63.688531 0 0 508.587274 71.491241 0L324.296479 540.228925l116.171859 0c5.454222 0.627287 5.383614 0.445138 11.004635 0.445138C529.296433 540.674063 592.384283 478.565517 592.384283 401.935232zM440.467315 468.738707 324.295456 468.738707 324.295456 334.690712l116.171859 0c33.95637 1.592264 65.084321 32.638351 65.084321 66.463738S474.423685 467.132116 440.467315 468.738707zM766.335081 647.359874c-2.26867-4.52199-8.481162-9.670244-8.481162-9.670244l-25.63996-68.644403c-5.323239-13.002129-20.98801-19.862373-27.821649-19.862373L569.737494 549.182854c-6.842849 0-22.497387 6.860245-27.821649 19.862373l-25.647123 68.644403c0 0-6.204305 5.148253-8.482185 9.670244-2.278903 4.501524-4.755304 8.525164-4.755304 38.956244 0 29.688159 5.950526 40.238446 13.07069 43.895743l5.629207 3.176342 0 27.558659c0 8.936533 2.121314 11.589966 10.603499 11.589966l19.041682 0c8.482185 0 14.198374 0.034792 14.198374-8.900717l0-26.808576 142.980435 0 0 26.808576c0 8.93551 5.716188 8.900717 14.198374 8.900717l19.041682 0c8.482185 0 10.602476-2.653433 10.602476-11.589966l0-27.558659 5.629207-3.176342c7.121188-3.656273 13.072737-14.207583 13.072737-43.895743C771.100618 655.886061 768.62217 651.862421 766.335081 647.359874zM539.813974 632.959909c1.196245-4.363378 9.188266-38.520315 12.730952-50.353828 1.450025-4.082992 10.927887-16.266475 21.966291-15.568581l125.108393 0c11.039427-0.697895 20.517289 11.485589 21.947871 15.568581 3.561106 11.833513 11.536754 45.99045 12.749372 50.353828 1.423419 5.184069-3.27151 4.729721-3.27151 4.729721L543.086507 637.68963C543.086507 637.68963 538.382369 638.145002 539.813974 632.959909zM553.17426 700.627055c-9.127891 0-16.545838-7.625678-16.545838-16.963347 0-9.356089 7.417947-16.965393 16.545838-16.965393 9.110495 0 16.528442 7.610328 16.528442 16.965393C569.703725 693.001377 562.284755 700.627055 553.17426 700.627055zM663.874501 710.017936l-53.617152 0 0-8.93551 53.617152 0L663.874501 710.017936zM663.874501 692.14487l-53.617152 0 0-8.93551 53.617152 0L663.874501 692.14487zM663.874501 674.271804l-53.617152 0 0-8.934486 53.617152 0L663.874501 674.271804zM721.584877 700.627055c-9.120728 0-16.546861-7.625678-16.546861-16.963347 0-9.356089 7.426133-16.965393 16.546861-16.965393 9.110495 0 16.535605 7.610328 16.535605 16.965393C738.121506 693.001377 730.696396 700.627055 721.584877 700.627055z" fill="#1296db" p-id="18541"></path></svg>
            </div> -->
            <div style="height: 50px; width: 200px"></div>
            <div class="contents_garage_tails">
                <p>
                    发送消息总数
                    <span>345</span>
                    &nbsp;&nbsp;个
                </p>
                <p>
                    接收消息总数
                    <span>234</span>
                    &nbsp;&nbsp;个
                </p>
            </div>
        </div>
    </div>
</template>

<script>
export default {
    data() {
        return {
            configCpu: {
                data: [66],
                formatter: '{value}%',
                waveHeight: 8,
                shape: 'round',
                waveNum: 1.5,
                waveOpacity: 0.4,
                colors: ['#00BAFF', '#3DE7C9'],
            },
        };
    },
};
</script>
