<template>
    <div id="chart2" style="width: 100%; height:100%; position: relative;"></div>
</template>
  
  <script>
  import * as echarts from 'echarts';
  
  export default {
    name: 'Chart',
    data() {
      return {
        chartInstance: null,
        dataC1: [100, 446, 212, 878, 12, 960, 287],
        xData: ['00:00', '04:00', '08:00', '12:00', '16:00', '20:00'],
      };
    },
    mounted() {
      this.initChart();
    },
    beforeDestroy() {
      if (!this.chartInstance) {
        return;
      }
      this.chartInstance.dispose();
      this.chartInstance = null;
    },
    methods: {
      initChart() {
        this.chartInstance = echarts.init(document.getElementById('chart2'));
        const option = {
          backgroundColor: 'transparent',
          xAxis: {
            type: 'category',
            axisLine: { show: false },
            axisTick: { show: false },
            axisLabel: { show: false },
            data: this.xData,
          },
          yAxis: {
            type: 'value',
            axisLine: { show: false },
            splitLine: { show: false },
            axisTick: { show: false },
            axisLabel: { show: false },
            boundaryGap: ['20%', '20%'],
          },
          series: [
            {
              name: '',
              type: 'line',
              stack: '总量',
              smooth: true,
              symbol: 'none',
              showSymbol: false,
              symbolSize: 8,
              itemStyle: {
                normal: {
                  lineStyle: {
                    color: {
                      type: 'linear',
                      x: 0,
                      y: 0,
                      x2: 0,
                      y2: 1,
                      colorStops: [
                        { offset: 0, color: '#9085FF' },
                        { offset: 0.8, color: '#674FE0' },
                        { offset: 1, color: '#674FE0' },
                      ],
                      global: false,
                    },
                    width: 5,
                  },
                  areaStyle: { opacity: 0 },
                },
              },
              data: this.dataC1,
            },
          ],
        };
  
        this.chartInstance.setOption(option);
      }
    }
  }
  </script>
  
  <style scoped>
  /* 你可以在这里添加一些样式 */
  </style>