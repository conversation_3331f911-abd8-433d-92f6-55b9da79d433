<template>
    <div class="echart-wrap">
        <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch"
            label-width="48px">
            <el-form-item label="名称" prop="echartName">
                <el-input style="width: 240px" v-model="queryParams.echartName" placeholder="请输入名称" clearable
                    @keyup.enter.native="handleQuery" />
            </el-form-item>
            <el-form-item label="类型" prop="echartType">
                <el-select style="width: 240px" v-model="queryParams.echartType" placeholder="请选择类型" clearable>
                    <el-option v-for="dict in dict.type.scada_echart_type" :key="dict.value" :label="dict.label"
                        :value="dict.value" @keyup.enter.native="handleQuery" />
                </el-select>
            </el-form-item>
            <el-form-item>
                <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
                <el-button icon="el-icon-refresh" size="mini" @click="handleResetQuery">重置</el-button>
            </el-form-item>
        </el-form>

        <el-row :gutter="10" class="mb10">
            <el-col :span="1.5">
                <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="handleAdd"
                    v-hasPermi="['scada:echart:add']">新增</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="success" plain icon="el-icon-edit" size="mini" :disabled="single" @click="handleUpdate"
                    v-hasPermi="['scada:echart:edit']">修改</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="danger" plain icon="el-icon-delete" size="mini" :disabled="multiple"
                    @click="handleDelete" v-hasPermi="['scada:echart:remove']">删除</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="warning" plain icon="el-icon-download" size="mini" @click="handleExport"
                    v-hasPermi="['scada:echart:export']">导出</el-button>
            </el-col>
            <template>
                <el-tooltip effect="dark" content="切换卡片/列表" placement="top" style="float: right;margin-right: 5px;">
                    <el-button size="mini" circle icon="el-icon-s-grid" @click="handleChangeShowType" />
                </el-tooltip>
            </template>
            <right-toolbar :showSearch.sync="showSearch" @queryTable="getList">
            </right-toolbar>
        </el-row>

        <div v-if="showType == 'card'">
            <el-row :gutter="15" v-loading="loading">
                <el-checkbox-group v-model="ids" @change="checkboxChange">
                    <el-col :xs="24" :sm="12" :md="8" :lg="6" :xl="6" style="margin-top: 7.5px; margin-bottom: 7.5px"
                        v-for="item in echartList" :key="item.id">
                        <el-card class="card-wrap" :body-style="{ padding: '10px' }">
                            <div class="img-wrap">
                                <el-image style="width: 100%; height: 100%; border-radius: 5px" lazy
                                    :src="baseApi + item.echartImgae" fit="cover" @click="goToDetail(item)"></el-image>
                            </div>
                            <div class="tag-wrap">
                                <span>{{ item.echartType }}</span>
                            </div>
                            <div class="name-wrap">
                                <span>{{ item.echartName }}</span>
                            </div>
                            <div class="tools-wrap">
                                <el-checkbox class="checkbox" :label="item.id" :key="item.id"><span
                                        v-show="false">占位符</span></el-checkbox>
                                <el-button-group>
                                    <el-button size="mini" type="text" icon="el-icon-view" @click="goToDetail(item)"
                                        v-hasPermi="['scada:echart:query']">详情</el-button>
                                    <el-button style="margin-left: 10px; color: #f56c6c" size="mini" type="text"
                                        icon="el-icon-delete" @click="handleDelete(item)"
                                        v-hasPermi="['scada:echart:remove']">删除</el-button>
                                </el-button-group>
                            </div>
                        </el-card>
                    </el-col>
                </el-checkbox-group>
            </el-row>
            <el-empty description="暂无数据" v-if="total == 0"></el-empty>
            <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum"
                :limit.sync="queryParams.pageSize" @pagination="getList" />
        </div>
        <div v-if="showType == 'list'">
            <el-table v-loading="loading" :data="echartList" @selection-change="handleSelectionChange">
                <el-table-column type="selection" width="55" align="center" />
                <el-table-column label="id" align="center" prop="id" width="100" />
                <el-table-column label="名称" align="center" prop="echartName" />
                <el-table-column label="类型" align="center" prop="echartType" width="100">
                    <template slot-scope="scope">
                        <el-tag type="info">{{ scope.row.echartType }}</el-tag>
                    </template>
                </el-table-column>
                <el-table-column label="缩略图" align="center" prop="echartImgae" width="120">
                    <template slot-scope="scope">
                        <image-preview :src="scope.row.echartImgae" :width="50" :height="50" />
                    </template>
                </el-table-column>
                <el-table-column label="更新时间" align="center" prop="updateTime" width="180" />
                <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="120">
                    <template slot-scope="scope">
                        <el-button size="mini" type="text" icon="el-icon-view" @click="goToDetail(scope.row)"
                            v-hasPermi="['scada:echart:query']">详情</el-button>
                        <el-button style="color: #f56c6c" size="mini" type="text" icon="el-icon-delete"
                            @click="handleDelete(scope.row)" v-hasPermi="['scada:echart:remove']">删除</el-button>
                    </template>
                </el-table-column>
            </el-table>
            <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum"
                :limit.sync="queryParams.pageSize" @pagination="getList" />
        </div>

        <!-- 添加图表对话框 -->
        <el-dialog :close-on-click-modal="false" :title="dialog.title" :visible.sync="dialog.open" width="400px"
            append-to-body>
            <div class="el-divider el-divider--horizontal" style="margin-top: -25px"></div>
            <el-form ref="dialogForm" :model="dialog.form" :rules="dialog.rules" label-width="58px">
                <el-form-item label="图片" prop="echartImgae">
                    <image-upload v-model="dialog.form.echartImgae" :multiple="false"
                        :class="{ disable: uploadDisabled }" />
                </el-form-item>
                <el-form-item label="名称" prop="echartName">
                    <el-input v-model="dialog.form.echartName" placeholder="请输入名称" clearable />
                </el-form-item>
                <el-form-item label="类型" prop="echartType">
                    <el-select style="width: 100%" v-model="dialog.form.echartType" placeholder="请选择类型" clearable>
                        <el-option v-for="dict in dict.type.scada_echart_type" :key="dict.value" :label="dict.label"
                            :value="dict.value"></el-option>
                    </el-select>
                </el-form-item>
            </el-form>
            <div slot="footer" class="dialog-footer">
                <el-button type="primary" @click="handleDialogSubmit">确 定</el-button>
                <el-button @click="handleDialogCancel">取 消</el-button>
            </div>
        </el-dialog>
    </div>
</template>

<script>
import { listEchart, getEchart, delEchart, addEchart, updateEchart } from '@/api/scada/echart';

export default {
    name: 'Echart',
    dicts: ['scada_echart_type'],
    computed: {
        uploadDisabled: function () {
            return this.dialog.form.echartImgae !== '';
        },
    },
    data() {
        return {
            loading: true, // 遮罩层
            baseApi: process.env.VUE_APP_BASE_API,
            single: true, // 非单个禁用
            multiple: true, // 非多个禁用
            showSearch: true, // 显示搜索条件
            // 查询参数
            queryParams: {
                pageNum: 1,
                pageSize: 10,
                orderByColumn: 'id',
                isAsc: 'desc',
                guid: null,
                deptIdStrs: null,
                echartType: null,
                echartData: null,
            },
            echartList: [], // 图表管理表格数据
            total: 0, // 总条数
            ids: [], // 选中数组
            showType: 'card',
            dialog: {
                open: false, // 弹出层标题
                title: '', // 对话框标题
                // 表单参数
                form: {
                    echartImgae: '',
                    echartName: '',
                    echartType: '',
                },
                // 表单校验
                rules: {
                    echartName: [{ required: true, message: '请输入名称', trigger: 'change' }],
                    echartType: [{ required: true, message: '请选择类型', trigger: 'change' }],
                },
            },
        };
    },
    created() {
        this.getList();
    },
    methods: {
        // 查询图表管理列表
        getList() {
            this.loading = true;
            listEchart(this.queryParams).then((response) => {
                if (response.code === 200) {
                    this.echartList = response.rows;
                    this.total = response.total;
                }
                this.loading = false;
            });
        },
        // 搜索按钮操作
        handleQuery() {
            this.queryParams.pageNum = 1;
            this.getList();
        },
        // 重置按钮操作
        handleResetQuery() {
            this.resetForm('queryForm');
            this.handleQuery();
        },
        // 多选框选中数据
        handleSelectionChange(selection) {
            this.ids = selection.map((item) => item.id);
            this.single = selection.length !== 1;
            this.multiple = !selection.length;
        },
        // 新增按钮操作
        handleAdd() {
            this.reset();
            this.dialog.open = true;
            this.dialog.title = '添加图表管理';
        },
        // 提交按钮
        handleDialogSubmit() {
            this.$refs['dialogForm'].validate((valid) => {
                if (valid) {
                    if (this.dialog.form.id != null) {
                        updateEchart(this.dialog.form).then((res) => {
                            if (res.code === 200) {
                                this.$modal.msgSuccess('修改成功');
                                this.dialog.open = false;
                                this.getList();
                            }
                        });
                    } else {
                        addEchart(this.dialog.form).then((res) => {
                            if (res.code === 200) {
                                this.$modal.msgSuccess('新增成功');
                                this.dialog.open = false;
                                this.getList();
                            }
                        });
                    }
                }
            });
        },
        // 取消按钮
        handleDialogCancel() {
            this.dialog.open = false;
        },
        // 修改按钮操作
        handleUpdate(row) {
            this.dialog.title = '修改图表管理';
            const id = row.id || this.ids;
            getEchart(id).then((res) => {
                if (res.code === 200) {
                    this.dialog.form = res.data;
                    this.dialog.open = true;
                }
            });
        },
        // 删除按钮操作
        handleDelete(row) {
            const ids = row.id || this.ids;
            this.$modal
                .confirm('是否确认删除图表编号为"' + ids + '"的数据项？')
                .then(function () {
                    return delEchart(ids);
                })
                .then(() => {
                    this.getList();
                    this.$modal.msgSuccess('删除成功');
                })
                .catch(() => { });
        },
        // 导出按钮操作
        handleExport() {
            this.download(
                'scada/echart/export',
                {
                    ...this.queryParams,
                },
                `图表${new Date().getTime()}.xlsx`
            );
        },
        // 详情页面
        goToDetail(row) {
            this.$router.push({
                path: '/scada/echart/detail',
                query: {
                    id: row.id,
                },
            });
        },
        // 表单重置
        reset() {
            this.dialog.form = {
                echartImgae: '',
                echartName: '',
                echartType: '',
            };
            this.resetForm('dialogForm');
        },
        // 切换显示方式
        handleChangeShowType() {
            this.ids = [];
            this.showType = this.showType == 'card' ? 'list' : 'card';
        },
        // 卡片选择
        checkboxChange(selection) {
            this.single = selection.length != 1;
            this.multiple = !selection.length;
        },
    },
};
</script>
<style lang="scss" scoped>
.echart-wrap {
    padding: 20px;

    .card-wrap {
        position: relative;
        border-radius: 5px;

        .img-wrap {
            height: 200px;
            width: 100%;
            // transition: transform 0.3s ease;

            // &:hover {
            //     cursor: pointer;
            //     transform: scale(1.1);
            // }
        }

        .tag-wrap {
            position: absolute;
            top: 0;
            left: 0;
            background-color: #1890ff;
            border-top-left-radius: 5px;
            border-bottom-right-radius: 5px;
            padding: 5px 15px;
            font-size: 12px;
            color: #fff;
        }

        .name-wrap {
            height: 20px;
            line-height: 20px;
            margin-top: 10px;
            font-size: 14px;
        }

        .tools-wrap {
            margin-top: 10px;
            display: flex;
            flex-direction: row;
            justify-content: space-between;
            align-items: center;
        }
    }
}

.disable {
    ::v-deep .el-upload--picture-card {
        display: none !important;
    }
}
</style>
