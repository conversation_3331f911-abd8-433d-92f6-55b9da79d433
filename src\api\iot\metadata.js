import request from '@/utils/request'

// 查询规则引擎元数据列表
export function listMetadata(query) {
  return request({
    url: '/iot/metadata/list',
    method: 'get',
    params: query
  })
}

// 查询规则引擎元数据详细
export function getMetadata(id) {
  return request({
    url: '/iot/metadata/' + id,
    method: 'get'
  })
}

// 新增规则引擎元数据
export function addMetadata(data) {
  return request({
    url: '/iot/metadata',
    method: 'post',
    data: data
  })
}

// 修改规则引擎元数据
export function updateMetadata(data) {
  return request({
    url: '/iot/metadata',
    method: 'put',
    data: data
  })
}

// 删除规则引擎元数据
export function delMetadata(id) {
  return request({
    url: '/iot/metadata/' + id,
    method: 'delete'
  })
}
