<template>
  <div class="app-container">
    <el-row :gutter="20">
      <!--公共菜单数据-->
      <el-col :span="4" :xs="24">
        <div class="head-container">
          <el-input v-model="deptName" placeholder="请输入菜单名称" clearable size="small" prefix-icon="el-icon-search"
            style="margin-bottom: 20px" />
        </div>
        <div class="head-container">
          <el-tree :data="deptOptions" :props="defaultProps" :expand-on-click-node="false"
            :filter-node-method="filterNode" ref="tree" node-key="id" default-expand-all highlight-current
            @node-click="handleNodeClick" />
        </div>
      </el-col>

      <!--用户数据-->
      <el-col :span="20">
        <div class="card-container">
          <el-descriptions :column="4" border class="el-descriptions">
            <el-descriptions-item label="设备总量" label-align="center">
              <el-tag class="el-tag" type="primary">{{ monitorItems.total }}</el-tag>
              <el-text class="mx-1" size="small">个</el-text>
            </el-descriptions-item>
            <el-descriptions-item label="在线设备" label-align="center">
              <img :src="baseApi + '/profile/gallery/video/down1.svg'">
              <el-tag class="el-tag" type="success">{{ monitorItems.online }}</el-tag>
              <el-text class="mx-1" size="large">个</el-text>
            </el-descriptions-item>
            <el-descriptions-item label="异常设备" label-align="center">
              <img :src="baseApi + '/profile/gallery/video/down2.svg'">
              <el-tag class="el-tag" type="danger">{{ monitorItems.offline }}</el-tag>
              <el-text class="mx-1" size="large">个</el-text>
            </el-descriptions-item>
            <el-descriptions-item label="离线设备" label-align="center">
              <img :src="baseApi + '/profile/gallery/video/down3.svg'">
              <el-tag class="el-tag" type="info">{{ monitorItems.abnormal }}</el-tag>
              <el-text class="mx-1" size="large">个</el-text>
            </el-descriptions-item>
          </el-descriptions>
          <el-card :body-style="{ width: '100%', padding: '6px 6px 6px 6px', height: '90vh' }">
            <!-- <div v-for="(image, index) in images" :key="index" class="draggable-image" >
              <el-image
                :src="baseApi + image.resourceUrl"
                :draggable="isDraggable" fit="cover"
              />
            </div> -->
            <el-empty style="margin: 200px;" description="暂无数据" v-if="geographyUrl == ''"></el-empty>
            <div class="draggable-image-container" v-show="geographyUrl != ''">
              <el-image class="geography_image" :src="baseApi + geographyUrl" fit="cover" />
              <div v-for="(item, index) in items" :key="index"
                :style="{ left: item.dragStartX + 'px', top: item.dragStartY + 'px' }" class="draggable-image">
                <img :src="baseApi + item.resourceUrl" alt="Draggable Image"
                  @mouseenter="handleMouseEnter(item.tiePointId)" @dblclick="handleDblClick(item.tiePointId)">
              </div>
            </div>
            <!-- <el-skeleton-item v-show="geographyUrl == ''" variant="image" description="暂无数据" style="width: 100%; height: 100%;" /> -->
            <!-- <el-empty description="暂无数据" v-if="geographyUrl == ''"></el-empty> -->
          </el-card>
        </div>
      </el-col>
    </el-row>

    <!-- 选择设备弹窗 -->
    <el-dialog :close-on-click-modal="false" :title="title" v-if="open" :visible.sync="open" align-center width="55vw">
      <el-main style="padding: 0;">
        <div style="height: 58vh; width: 70.5%; display: flex; flex-wrap: wrap; float: left; background-color: #00F; border-radius: 6px;
            display: flex; flex-wrap: wrap; align-items: center; justify-content: center;">
          <div v-for="i in spilt" :key="i" class="play-box" :style="liveStyle"
            :class="{ redborder: playerIdx == i - 1 }" @click="playerIdx = i - 1">
            <div v-if="!videoUrl[i - 1]" style="color: #ffffff; font-size: 30px; font-weight: bold">{{ i }}</div>
            <player ref="player" v-else :videoUrl="videoUrl[i - 1]" fluent autoplay @screenshot="shot"
              @destroy="destroy" class="player-wrap" />
          </div>
        </div>
        <div
          style="height: 58vh; width: 28%; display: flex; flex-wrap: wrap; float: right; border: 2px solid #409EFF !important; cursor: pointer;">
          <div class="el-info-descriptions" style="height: 50%; width: 100%; padding: 6px; ">
            <el-row
              style="padding: 0 6px 0 6px;  border: 1px solid #409EFF !important; overflow-y: hidden; height: 100%; color: #E6E8EB; display: flex; flex-direction: column; justify-content: space-between; font-size: 0.8vw;">
              <el-col :span="24">设备名称：{{ this.monitorInfo.deviceName }}</el-col>
              <el-col :span="24"
                style="font-size: 0.8vw; white-space: nowrap; overflow: hidden; text-overflow: ellipsis; ">设备编号：
                {{ this.monitorInfo.serialNumber }}
              </el-col>
              <el-col :span="24">产品名称：{{ this.monitorInfo.productName }}</el-col>
              <el-col :span="24">
                设备状态： <span style="display: inline-block;">
                  <dict-tag :options="dict.type.iot_device_status" :value="this.monitorInfo.status" size="small"
                    effect="dark" />
                </span>
              </el-col>
              <el-col :span="24">信号强度：
                <div style="font-size: 22px; color: #ccc; display: inline-block; position: absolute; margin: 0 4px;">
                  <svg-icon v-if="this.monitorInfo.status == 3 && this.monitorInfo.rssi >= '-55'" icon-class="wifi_4" />
                  <svg-icon
                    v-else-if="this.monitorInfo.status == 3 && this.monitorInfo.rssi >= '-70' && item.rssi < '-55'"
                    icon-class="wifi_3" />
                  <svg-icon
                    v-else-if="this.monitorInfo.status == 3 && this.monitorInfo.rssi >= '-85' && item.rssi < '-70'"
                    icon-class="wifi_2" />
                  <svg-icon
                    v-else-if="this.monitorInfo.status == 3 && this.monitorInfo.rssi >= '-100' && item.rssi < '-85'"
                    icon-class="wifi_1" />
                  <svg-icon v-else icon-class="wifi_0" />
                </div>
              </el-col>
              <el-col :span="24">设备入网IP：{{ this.monitorInfo.networkIp }}</el-col>
              <el-col :span="24"
                style="font-size: 0.8vw; white-space: nowrap; overflow: hidden; text-overflow: ellipsis;">备注：{{
                  this.monitorInfo.remark }}</el-col>
            </el-row>
            <!-- <el-descriptions :column="1" size="size" style="overflow-y: hidden; height: 100%; background-color: #0F0; display: flex; flex-direction: column; justify-content: space-between; font-size: 0.88vw;">
              <el-descriptions-item label="设备名称">{{ this.monitorInfo.deviceName }}</el-descriptions-item>
              <el-descriptions-item label="设备编号">
                <el-text class="w-100px mb-2" truncated>
                  {{ this.monitorInfo.serialNumber }}
                </el-text>
              </el-descriptions-item>
              <el-descriptions-item label="产品名称">{{ this.monitorInfo.productName }}</el-descriptions-item>
              <el-descriptions-item label="设备状态">{{ this.monitorInfo.status }}</el-descriptions-item>
              <el-descriptions-item label="信号强度">{{ this.monitorInfo.rssi }}</el-descriptions-item>
              <el-descriptions-item label="设备入网IP">{{ this.monitorInfo.networkIp }}</el-descriptions-item>
              <el-descriptions-item label="设备所在地址">{{ this.monitorInfo.networkAddress }}</el-descriptions-item>
            </el-descriptions> -->
          </div>
          <div style="height: 50%; width: 100%; padding: 6px;">
            <div style="border: 1px solid #409EFF !important; height: 100%;">
              <!-- 遥控按钮-->
              <div class="control-wrapper">
                <div class="control-btn control-top" @dblclick="onUpperClick"></div>
                <div class="control-btn control-left" @dblclick="onLeftClick"> </div>
                <div class="control-btn control-bottom" @dblclick="onBelowClick"></div>
                <div class="control-btn control-right" @dblclick="onRightClick"></div>
                <div class="control-round" style="background-color:#000000;">
                  <div class="control-round-inner" @dblclick="onRefreshClick"></div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </el-main>
    </el-dialog>
  </div>
</template>

<script>
import { listGallery } from '@/api/subsystem/gallery';//加载所有状态为 1正常 的图标
import { findTiePointDeviceByTiePointId } from "@/api/subsystem/tiepointDevice";//查询子系统的绑点关联关系
import { getDevice } from "@/api/iot/device";//查询设备详情
import { getDicts } from '@/api/system/dict/data';
import { deptTreeSelect, getGeography } from "@/api/subsystem/geography";
import { monitorReportStatistics } from "@/api/system/floor";
import { findTiePointByfloorIdAndCategoryName } from "@/api/subsystem/tiepoint";//查询绑定的点位
import GeographyDialog from './geography-dialog.vue';//绑定设置弹窗
import Treeselect from "@riophae/vue-treeselect";
import "@riophae/vue-treeselect/dist/vue-treeselect.css";
import draggable from 'vuedraggable'
import { startPlay } from '@/api/iot/channel';
import player from '@/views/components/player/jessibuca.vue';

export default {
  name: "User",
  dicts: ['subsystem_gallery_icon_type', 'sys_floor_type', 'iot_device_status'],
  components: {
    Treeselect,
    draggable,
    GeographyDialog,
    player
  },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 根路径
      baseApi: process.env.VUE_APP_BASE_API,
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      galleryList: [],  // 图库管理表格数据
      geographyUrl: "", // 楼层CAD背景图

      // 部门树选项
      deptOptions: undefined,
      // 部门名称
      deptName: undefined,
      // 日期范围
      dateRange: [],
      // 岗位选项
      postOptions: [],
      // 角色选项
      roleOptions: [],
      defaultProps: {
        children: "children",
        label: "label"
      },
      //模块类型查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 20,
        categoryName: '',
        galleryName: '',
        resourceUrl: null,
        status: null,
        isAsc: 'desc',
      },
      queryGeographyParams: {
        floorId: undefined,
        galleryId: undefined,
        dragStartX: 0,
        dragStartY: 0
      },
      items: [],
      draggingIndex: null,
      dragStartAxleX: 0,
      dragStartAxleY: 0,
      dragGaTiepointId: undefined,
      monitorFloorId: 0,//选中的菜单编号
      monitorItems: [],
      monitorInfo: [],
      videoUrl: [''],
      spilt: 1, //分屏
      playerIdx: 0, //激活播放器
      updateLooper: 0, //数据刷新轮训标志
      count: 15,
      total: 0,
    };
  },
  created() {
    this.getDeptTree();//页面初始化查询左侧公共菜单
    // 获取数据
    this.getDatas();
    // this.getGeographyUrl();
    //查询视频监控统计报表
    this.getMonitorReportStatistics();
    this.checkPlayByParam();
  },
  computed: {
    liveStyle() {
      let style = { width: '100%', height: '100%' };
      this.$nextTick(() => {
        for (let i = 0; i < this.spilt; i++) {
          const player = this.$refs.player;
          player && player[i] && player[i].updatePlayerDomSize();
        }
      });
      return style;
    },
  },
  watch: {
    // 根据名称筛选部门树
    deptName(val) {
      this.$refs.tree.filter(val);
    },
    spilt(newValue) {
      console.log('切换画幅;' + newValue);
      let that = this;
      for (let i = 1; i <= newValue; i++) {
        if (!that.$refs['player' + i]) {
          continue;
        }
        this.$nextTick(() => {
          if (that.$refs['player' + i] instanceof Array) {
            that.$refs['player' + i][0].resize();
          } else {
            that.$refs['player' + i].resize();
          }
        });
      }
      window.localStorage.setItem('split', newValue);
    },
    '$route.fullPath': 'checkPlayByParam',
  },
  destroyed() {
    clearTimeout(this.updateLooper);
  },
  methods: {
    //遥控按钮事件-上
    onUpperClick() {
      alert("上")
    },
    //遥控按钮事件-下
    onBelowClick() {
      alert("下")
    },
    //遥控按钮事件-左
    onLeftClick() {
      alert("左")
    },
    //遥控按钮事件-右
    onRightClick() {
      alert("右")
    },
    //遥控按钮事件-刷新
    onRefreshClick() {
      alert("刷新")
    },

    getMonitorReportStatistics() {
      if (this.queryGeographyParams.floorId != undefined) {
        this.monitorFloorId = this.queryGeographyParams.floorId;
      }
      monitorReportStatistics(this.monitorFloorId).then((response) => {
        if (response.code === 200) {
          this.monitorItems = response.data;
        }
      });
    },
    //双击事件
    handleDblClick(tiePointId) {
      //判断当前子系统图标在库中是否已经存在绑点的关联关系
      findTiePointDeviceByTiePointId(tiePointId).then((response) => {
        if (response.code === 200) {
          if (response.data == undefined) {//说明没有关联关系
            this.$modal.msgError("暂未绑点监控设备！");
          } else {
            getDevice(response.data.deviceId).then((response) => {
              this.monitorInfo = response.data;
              this.title = "【设备：" + response.data.deviceName + "】";
              //通知设备上传媒体流
              this.sendDevicePush(response.data);
            })
            this.open = true;
          }
        }
      })
    },
    destroy(idx) {
      console.log(idx);
      this.clear(idx.substring(idx.length - 1));
    },
    //通知设备上传媒体流
    sendDevicePush: function (itemData) {
      this.save(itemData);
      let deviceId = itemData.serialNumber;
      let channelId = itemData.serialNumber;
      console.log('通知设备推流1：' + deviceId + ' : ' + channelId);
      let idxTmp = this.playerIdx;
      let that = this;
      this.loading = true;
      startPlay(deviceId, channelId).then((response) => {
        console.log('开始播放：' + deviceId + ' : ' + channelId);
        console.log('流媒体信息：' + response.data);
        let res = response.data;
        console.log('playurl：' + res.playurl);
        itemData.playUrl = res.playurl;
        itemData.streamId = res.streamId;
        that.setPlayUrl(itemData.playUrl, idxTmp);
      })
        .finally(() => {
          that.loading = false;
        });
    },
    setPlayUrl(url, idx) {
      this.$set(this.videoUrl, idx, url);
      let _this = this;
      setTimeout(() => {
        window.localStorage.setItem('videoUrl', JSON.stringify(_this.videoUrl));
      }, 100);
    },
    checkPlayByParam() {
      let { deviceId, channelId } = this.$route.query;
      if (deviceId && channelId) {
        this.sendDevicePush({ deviceId, channelId });
      }
    },
    shot(e) {
      var base64ToBlob = function (code) {
        let parts = code.split(';base64,');
        let contentType = parts[0].split(':')[1];
        let raw = window.atob(parts[1]);
        let rawLength = raw.length;
        let uInt8Array = new Uint8Array(rawLength);
        for (let i = 0; i < rawLength; ++i) {
          uInt8Array[i] = raw.charCodeAt(i);
        }
        return new Blob([uInt8Array], {
          type: contentType,
        });
      };
      let aLink = document.createElement('a');
      let blob = base64ToBlob(e); //new Blob([content]);
      let evt = document.createEvent('HTMLEvents');
      evt.initEvent('click', true, true); //initEvent 不加后两个参数在FF下会报错  事件类型，是否冒泡，是否阻止浏览器的默认行为
      aLink.download = '截图';
      aLink.href = URL.createObjectURL(blob);
      aLink.click();
    },
    save(item) {
      let dataStr = window.localStorage.getItem('playData') || '[]';
      let data = JSON.parse(dataStr);
      data[this.playerIdx] = item;
      window.localStorage.setItem('playData', JSON.stringify(data));
    },
    clear(idx) {
      let dataStr = window.localStorage.getItem('playData') || '[]';
      let data = JSON.parse(dataStr);
      data[idx - 1] = null;
      console.log(data);
      window.localStorage.setItem('playData', JSON.stringify(data));
    },
    //鼠标移入事件
    handleMouseEnter(tiePointId) {
      console.log(tiePointId);
    },
    //-----------------------------------------------------------------------------------------------------------------------------------------------------------
    //获取子系统图标
    async getDatas() {
      //const dictType = 'subsystem_gallery_icon_type';
      //const { data } = (await this.getCategoryTypes(dictType)) || [];
      this.queryParams.categoryName = '视频监控';
      this.queryParams.status = "1";
      this.getGalleryList();
    },
    // 获取种类
    getCategoryTypes(dictType) {
      return new Promise((resolve, reject) => {
        getDicts(dictType)
          .then((res) => {
            resolve(res);
          })
          .catch((error) => {
            reject(error);
          });
      });
    },
    getGalleryList() {
      this.loading = true;
      listGallery(this.queryParams).then((response) => {
        if (response.code === 200) {
          this.galleryList = response.rows;
          this.total = response.total;
        }
        this.loading = false;
      });
    },
    //模块类型选择
    handleGalleryChange(value) {
      this.queryParams.pageNum = 1;
      this.queryParams.categoryName = value;
      this.queryParams.status = "1";
      this.single = true;
      this.multiple = true;
      this.getGalleryList();
      this.queryCoordinate();
    },


    //-----------------------------------------------------------------------------------------------------------------------------------------------------------
    /** 查询部门下拉树结构 */
    getDeptTree() {
      deptTreeSelect().then(response => {
        this.deptOptions = response.data;
        this.queryGeographyParams.floorId = this.deptOptions[0].id;//获取左侧tree的根节点传给右侧查询楼层cad背景图
        this.getGeographyUrl();
      });
    },
    // 节点单击事件
    handleNodeClick(data) {
      this.queryGeographyParams.floorId = data.id;
      this.getGeographyUrl();
      this.getMonitorReportStatistics();
    },

    /** 查询楼层CAD背景图 */
    getGeographyUrl() {
      this.loading = true;
      getGeography(this.queryGeographyParams.floorId).then(response => {
        //判断查询菜单是否设置了背景图
        if (response.data == undefined) {
          this.geographyUrl = ""
        } else {
          this.geographyUrl = response.data.geographyUrl;
          this.queryCoordinate();
        }
      });
    },
    queryCoordinate() {
      //alert("根据该节点编号查询关于他的所有子系统图标===="+floorId);
      findTiePointByfloorIdAndCategoryName(this.queryGeographyParams.floorId, this.queryParams.categoryName).then(response => {
        this.items = response.data;
      });
    },
    // 筛选节点
    filterNode(value, data) {
      if (!value) return true;
      return data.label.indexOf(value) !== -1;
    },
  }
};
</script>

<style scoped>
/*------------------------------------------------------------ 遥控 开始------------------------------------------------------------ */
.control-btn:hover {
  color: black;
  background: #409eff;
}

.control-round-inner:hover {
  color: black;
  background: #409eff;
}

.control-wrapper {
  cursor: pointer;
  position: relative;
  width: 12vw;
  height: 12vw;
  max-width: 100%;
  max-height: 100%;
  min-width: 12vw;
  min-height: 12vw;
  margin: 0 auto;
  top: 50%;
  left: 43%;
  transform: translate(-50%, -50%);
  /* 同时修正水平和垂直位置 */
  border-radius: 50%;
  overflow-y: hidden;
  overflow-X: hidden;
}

.control-btn {
  position: absolute;
  width: 40%;
  height: 40%;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1px solid #409EFF;
  box-sizing: border-box;
  transition: all .3s linear;
}

.control-btn:after {
  content: '';
  position: absolute;
  width: 60%;
  height: 60%;
  background: #fff;
  z-index: 2;
}

.control-btn:before {
  content: '';
  position: relative;
  display: block;
  width: 16px;
  height: 16px;
  border-top: 3px solid #78aee4;
  border-right: 3px solid #78aee4;
  border-radius: 0 4px 0 0;
  box-sizing: border-box;
  z-index: 2;
}

.control-top {
  top: 0;
  left: 50%;
  transform: translateX(-50%) rotate(-45deg);
  border-radius: 4px 100% 4px 4px;
}

.control-top:before {
  transform: translate(20%, -20%);
}

.control-top:after {
  left: 0;
  bottom: 0;
  border-top: 1px solid #78aee4;
  border-right: 1px solid #78aee4;
  border-radius: 0 80% 0 0;
}

.control-bottom {
  left: 50%;
  bottom: 0;
  transform: translateX(-50%) rotate(45deg);
  border-radius: 4px 4px 100% 4px;
}

.control-bottom:before {
  transform: translate(20%, 20%) rotate(90deg);
}

.control-bottom:after {
  top: 0;
  left: 0;
  border-bottom: 1px solid #78aee4;
  border-right: 1px solid #78aee4;
  border-radius: 0 0 80% 0;
}

.control-left {
  top: 50%;
  left: 0;
  transform: translateY(-50%) rotate(45deg);
  border-radius: 4px 4px 4px 100%;
}

.control-left:before {
  transform: translate(-20%, 20%) rotate(180deg);
}

.control-left:after {
  right: 0;
  top: 0;
  border-bottom: 1px solid #78aee4;
  border-left: 1px solid #78aee4;
  border-radius: 0 0 0 80%;
}

.control-right {
  top: 50%;
  right: 0;
  transform: translateY(-50%) rotate(45deg);
  border-radius: 4px 100% 4px 4px;
}

.control-right:before {
  transform: translate(20%, -20%);
}

.control-right:after {
  left: 0;
  bottom: 0;
  border-top: 1px solid #78aee4;
  border-right: 1px solid #78aee4;
  border-radius: 0 80% 0 0;
}

.control-round {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 51.2%;
  height: 51.2%;
  background: #fff;
  border-radius: 50%;
}

.control-round-inner {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  display: flex;
  justify-content: center;
  align-items: center;
  width: 66%;
  height: 66%;
  border: 1px solid #78aee4;
  border-radius: 50%;
}

.control-round-inner:after {
  content: "| |";
  display: block;
  width: 50px;
  line-height: 50px;
  text-align: center;
  background: #78aee4;
  font-weight: bolder;
  color: #FFFFFF;
  border-radius: 50%;
}

/*------------------------------------------------------------ 遥控 结束------------------------------------------------------------ */

.btn {
  margin: 0 10px;
}

.btn:hover {
  color: #409eff;
}

.btn.active {
  color: #409eff;
}

.redborder {
  border: 2px solid red !important;
}

.play-box {
  background-color: #000000;
  border: 1px solid #505050;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 0px;
  position: relative;
  border-radius: 5px;
}

.player-wrap {
  position: absolute;
  top: 0px;
  height: 100% !important;
  width: 100% !important;
}

/* ---------------------------------------------------------------------------------------------------------------------------------------------- */

.el-descriptions-item {
  display: -webkit-box;
  -webkit-line-clamp: 1;
  /* 限制为一行 */
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

::v-deep .el-dialog {
  /* background: linear-gradient(135deg, rgba(3, 51, 241, 0.114) 45%, rgba(34, 32, 34, 0.2) 55%); */
  background: #201f1f;
  border-radius: 8px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

::v-deep .el-dialog__title {
  color: #FFFFFF;
}

::v-deep .el-dialog__headerbtn .el-dialog__close {
  color: #FFFFFF;
}

::v-deep .el-dialog__body {
  padding: 8px 14px 14px 14px;
  color: #606266;
  font-size: 14px;
  word-break: break-all;
}

.el-descriptions img {
  width: 30px;
  height: 30px;
  position: absolute;
  margin-top: 1px;
}

.el-descriptions .el-tag {
  font-size: 14px;
  margin: 2px 0 0 34px;
}

.el-descriptions .mx-1 {
  font-size: 14px;
  font-weight: 700;
  margin-left: 4px;
  color: #888888;
}

:deep(.my-label) {
  background: var(--el-color-success-light-9) !important;
}

:deep(.my-content) {
  background: var(--el-color-danger-light-9);
}

.demo_image_preview {
  display: flex;
  /* 使div变为Flex容器 */
  flex-wrap: nowrap;
  /* 避免图片换行 */
}

.card-container {
  margin-bottom: 20px;
  width: 100%;
  height: 100%;
  position: relative;
}

.drawing-board {
  height: 100%;
  position: relative;
}

.draggable-image-container {
  position: relative;
}

.geography_image {
  /** 楼层背景样式 */
  width: 100%;
  height: 88.6vh;
  z-index: 1;
  display: block;
}

.draggable-image {
  position: absolute;
  cursor: pointer;
  z-index: 999;
}

.draggable-image img {
  width: 40px;
  height: 45px;
}
</style>
