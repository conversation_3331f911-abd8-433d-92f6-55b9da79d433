<template>
  <!-- 导入分配设备弹窗 -->
  <el-dialog :close-on-click-modal="false" :title="upload.title" :visible.sync="upload.importAllotDialog" width="550px"
    append-to-body>
    <div style="margin-top: -55px">
      <el-divider style="margin-top: -30px"></el-divider>
      <el-form label-position="top" :model="allotForm" ref="allotForm" :rules="allotRules">
        <el-form-item label="产品" prop="productId">
          <el-select v-model="allotForm.productId" placeholder="请选择产品" filterable style="width: 100%;">
            <el-option v-for="item in productList" :key="item.value" :label="item.label" :value="item.value">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="目标机构" prop="deptId">
          <treeselect v-model="allotForm.deptId" :options="deptOptions" :show-count="true" placeholder="请选择目标机构"
            @select="handleDeptChange" />
        </el-form-item>
        <el-form-item label="所属场景" prop="scenarioId">
          <el-select v-model="allotForm.scenarioId" placeholder="请选择所属场景" clearable style="width: 100%"
            :disabled="!allotForm.deptId" @change="handleScenarioChange">
            <el-option v-for="scenario in scenarioOptions" :key="scenario.scenarioId" :label="scenario.scenarioName"
              :value="scenario.scenarioId" />
          </el-select>
        </el-form-item>
        <el-form-item label="上传文件" prop="fileList">
          <el-upload ref="upload" :limit="1" accept=".xlsx, .xls" :headers="upload.headers"
            :action="upload.url + '?productId=' + allotForm.productId + '&deptId=' + allotForm.deptId + '&scenarioId=' + allotForm.scenarioId"
            :disabled="upload.isUploading" :on-progress="handleFileUploadProgress" :on-success="handleFileSuccess"
            :auto-upload="false" :on-change="handleChange" :on-remove="handleRemove" drag v-model="allotForm.fileList">
            <i class="el-icon-upload"></i>
            <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
            <div class="el-upload__tip" slot="tip">
              <div style="margin-top: -5px;">
                <span>1.仅允许导入xls、xlsx格式文件。</span>
                <div style="margin-top: -10px;">
                  <span>2.单次最多分配1000个设备,单次设备较多时需要较长的校验、导入时间。</span>
                </div>
                <div style="margin-top: -10px;">
                  <span>3.上传文件并分配后，可到设备列表-更多操作-设备导入记录中查看上传失败的设备详情信息。</span>
                </div>
              </div>
            </div>
          </el-upload> <el-link type="primary" :underline="false" style="font-size:14px;vertical-align: baseline;"
            @click="importAllotTemplate"><i class="el-icon-download"></i>设备分配模板</el-link>
        </el-form-item>
      </el-form>
    </div>
    <div slot="footer" class="dialog-footer">
      <el-button @click="upload.importAllotDialog = false">取消</el-button>
      <el-button type="primary" @click="submitImportDevice">确认分配</el-button>
    </div>
  </el-dialog>
</template>
<script>
import { listProduct } from "@/api/iot/product";
import { getToken } from "@/utils/auth";
import { deptsTreeSelect } from "@/api/system/user";
import { listScenario } from '@/api/iot/scenario';
import Treeselect from "@riophae/vue-treeselect";
import "@riophae/vue-treeselect/dist/vue-treeselect.css";
export default {
  name: 'allotImport',
  components: {
    Treeselect
  },
  data() {
    return {
      type: 1,
      //导入表单
      allotForm: {
        productId: 0,
        deptId: 0,
        scenarioId: null,
        fileList: [],
      },
      productList: [],
      deptOptions: [],
      // 场景选项
      scenarioOptions: [],
      // 分配导入参数
      upload: {
        // 弹出层标题
        title: "导入分配",
        importAllotDialog: false,
        // 是否禁用上传
        isUploading: false,
        // 设置上传的请求头部
        headers: { Authorization: "Bearer " + getToken() },
        // 上传的地址
        url: process.env.VUE_APP_BASE_API + "/iot/device/importAssignmentData"
      },
      isSubDev: false,
      //导入分配表单校验
      allotRules: {
        productId: [{ required: true, message: '产品不能为空', trigger: 'change' }],
        deptId: [{ required: true, message: '目标机构不能为空', trigger: 'change' }],
        scenarioId: [{ required: true, message: '所属场景不能为空', trigger: 'change' }],
        fileList: [
          { required: true, message: '请上传文件', trigger: 'change' }
        ]
      },
    };
  },
  created() {
    this.getDeptTree();
    this.getProductList();
  },
  methods: {
    /** 查询机构下拉树结构 */
    getDeptTree() {
      deptsTreeSelect().then(response => {
        this.deptOptions = response.data;
      });
    },
    /** 机构选择改变 */
    handleDeptChange(node) {
      // 清空场景选择
      this.allotForm.scenarioId = null;
      this.scenarioOptions = [];

      if (node && node.id) {
        // 根据选中的机构获取对应的场景列表
        this.getScenarioList(node.id);
      }
    },
    /** 获取场景列表 */
    getScenarioList(tenantId) {
      const params = {
        tenantId: tenantId
      };
      listScenario(params).then((response) => {
        this.scenarioOptions = response.rows || response.data || [];
      });
    },
    /** 场景选择改变 */
    handleScenarioChange(value) {
      // 场景选择改变时可以进行其他处理
      console.log('Selected scenario:', value);
    },
    /** 下载分配导入模板操作 */
    importAllotTemplate() {
      this.type = 2;
      this.download('/iot/device/uploadTemplate?type=' + this.type, {
      },
        `allot_device_${new Date().getTime()}.xlsx`);
    },
    // 选择文件后给表单验证的prop字段赋值， 并且清除该字段的校验
    handleChange(file, fileList) {
      this.allotForm.fileList = fileList;
      if (this.allotForm.fileList) {
        this.$refs.allotForm.clearValidate('fileList');
      }
    },
    // 删除文件后重新校验该字段
    handleRemove(file, fileList) {
      this.allotForm.fileList = fileList;
      this.$refs.allotForm.validateField('fileList');
    },
    // 文件上传中处理
    handleFileUploadProgress(event, file, fileList) {
      this.upload.isUploading = true;
    },
    // 文件上传成功处理
    handleFileSuccess(response, file, fileList) {
      this.upload.open = false;
      this.upload.isUploading = false;
      this.$refs.upload.clearFiles();
      this.$alert("<div style='overflow: auto;overflow-x: hidden;max-height: 70vh;padding: 10px 20px 0;'>" + response.msg + "</div>", "导入结果", { dangerouslyUseHTMLString: true });
    },
    /** 查询产品列表 */
    getProductList() {
      this.loading = true;
      const params = {
        pageSize: 999,
      }
      listProduct(params).then(response => {
        this.productList = response.rows.map((item) => {
          return { value: item.productId, label: item.productName };
        });
        this.total = response.total;
        this.loading = false;
      });
    },
    //分配设备导入设备提交按钮
    submitImportDevice() {
      this.$refs['allotForm'].validate((valid) => {
        if (valid) {
          this.$refs.upload.submit();
          this.upload.importAllotDialog = false;
        }
      });
    },

  },
};
</script>
