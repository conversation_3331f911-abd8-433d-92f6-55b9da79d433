<template>
  <div id="chartContainer" class="chart-container"></div>
</template>

<script>
import * as echarts from 'echarts';

export default {
  name: 'Status<PERSON>ieC<PERSON>',
  data() {
    return {
      peopleList: [
        { name: '故障', value: 4, percent: 0 },
        { name: '离线', value: 10, percent: 0 },
        { name: '在线', value: 20, percent: 0 },
      ],
      chartInstance: null
    };
  },
  mounted() {
    this.initChart();
  },
  beforeDestroy() {
    if (this.chartInstance) {
      this.chartInstance.dispose();
      this.chartInstance = null;
    }
  },
  methods: {
    initChart() {
      const total = this.peopleList.reduce((pre, next) => pre + next.value, 0);
      const numberWidth = String(total).length * 8 + 8;

      this.peopleList.forEach(item => {
        item.percent = total === 0 ? 0 : parseFloat(((item.value / total) * 100).toFixed(2));
      });

      const color = ['rgba(113, 226, 135, 1)', 'rgba(119, 247, 253, 1)', 'rgba(44, 104, 231, 1)', 'rgba(93, 202, 250, 1)'];

      const option = {
        color: color,
        backgroundColor: '#FFFFFF',
        tooltip: {
          trigger: 'item',
        },
        legend: {
          show: true,
          orient: 'vertical',
          top: 'center',
          right: '20%',
          top:'73%',
          icon: 'rect',
          itemGap: 6,
          itemWidth: 20,
          itemHeight: 10,
          color: '#fff',
          formatter: (name) => {
            let items = this.peopleList.find(item => item.name === name);
            return `{name|${name}}  {number| ${items.value}} {unit|台}    {percent|${items.percent + '%' || ''}}`;
          },
          itemStyle: {
            borderWidth: 1,
          },
          textStyle: {
            rich: {
              number: {
                width: numberWidth,
                color: '#495057',
                align: 'left',
                fontSize: 14,
                fontWeight: 'bold',
                padding: [0, 0, 0, 0]
              },
              name: {
                color: '#495057',
                fontSize: 12,
                fontWeight: 400,
                fontFamily: 'Source Han Sans CN',
                padding: [0, 0, 0, 8]
              },
              unit: {
                color: '#495057',
                fontSize: 12,
                fontWeight: 400,
                fontFamily: 'Source Han Sans CN',
                padding: [0, 0, 0, 8]
              },
              percent: {
                color: '#495057',
                align: 'left',
                fontSize: 12,
                fontWeight: 600,
                padding: [0, 0, 0, 8]
              },
            },
          },
        },
        title: [
          {
            text: '{title|总数}',
            left: '48.5%',
            top: '36%',
            textAlign: 'center',
            textStyle: {
              rich: {
                title: {
                  color: '#495057',
                  fontSize: 14,
                  fontWeight: '400',
                },
              }
            },
          },
          {
            text: '{num|' + total + '}{unit|台}',
            left: '43.5%',
            top: '28%',
            textStyle: {
              rich: {
                num: {
                  fontSize: 18,
                  color: '#49F1F2',
                  fontFamily: 'DIN Alternate',
                  fontWeight: 'bold',
                },
                unit: {
                  color: '#495057',
                  fontSize: 12,
                  fontWeight: '400',
                  padding: [0, 0, 0, 0]
                }
              }
            },
          },
        ],
        series: [
          {
            type: 'pie',
            radius: ['45%', '65%'],
            center: ['50%', '35%'],
            padAngle: 5,
            label: {
              show: false,
            },
            itemStyle: {
              borderWidth: 5,
              borderColor: {
                type: 'linear',
                x: 0,
                y: 0,
                x2: 1,
                y2: 1,
                colorStops: [
                  { offset: 0, color: '#ffffff' },
                  { offset: 1, color: '#ffffff' }
                ]
              },
              opacity: 1,
              color: function (params) {
                const colorList = [
                  {
                    type: 'linear',
                    x: 0,
                    y: 0,
                    x2: 1,
                    y2: 1,
                    colorStops: [
                      { offset: 0, color: 'rgba(255, 87, 51, 0)' },
                      { offset: 1, color: 'rgba(255, 87, 51, 1)' }
                    ]
                  },
                  {
                    type: 'linear',
                    x: 0,
                    y: 0,
                    x2: 1,
                    y2: 1,
                    colorStops: [
                      { offset: 0, color: 'rgba(17, 135, 145, 0)' },
                      { offset: 1, color: 'rgba(17, 135, 145, 1)' }
                    ]
                  },
                  {
                    type: 'linear',
                    x: 0,
                    y: 0,
                    x2: 1,
                    y2: 1,
                    colorStops: [
                      { offset: 0, color: 'rgba(24, 132, 236, 0)' },
                      { offset: 1, color: 'rgba(24, 132, 236, 1)' }
                    ]
                  }
                ];
                return colorList[params.dataIndex % colorList.length];
              }
            },
            emphasis: {
              scale: false
            },
            data: this.peopleList,
          },
          {
            name: "黄线",
            type: "pie",
            startAngle: 85,
            radius: ['35%', '38%'],
            center: ['50%', '35%'],
            hoverAnimation: false,
            startAngle: 90,
            padAngle: 5,
            tooltip: {
              show: false,
            },
            itemStyle: {
              borderCap: 'round',
              normal: {
                color: function (data) {
                  let tempColor = data.data == 10 ? "#FFFFFF" : "#1884EC";
                  return tempColor;
                },
              },
            },
            zlevel: 4,
            labelLine: {
              show: false,
            },
            data: [10, 50, 10, 50, 10, 50, 10, 50],
          },
        ],
      };

      this.chartInstance = echarts.init(document.getElementById('chartContainer'));
      this.chartInstance.setOption(option);
    },
  }
};
</script>

<style scoped>
.chart-container {
  width: 100%;
  height: 100%;
  position: relative;
}
</style>