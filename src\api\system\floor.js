import request from '@/utils/request'

// 查询部门列表
export function listDept(query) {
  return request({
    url: '/system/floor/list',
    method: 'get',
    params: query
  })
}

// 查询部门列表（排除节点）
export function listDeptExcludeChild(floorId) {
  return request({
    url: '/system/floor/list/exclude/' + floorId,
    method: 'get'
  })
}

// 查询部门详细
export function getDept(floorId) {
  return request({
    url: '/system/floor/' + floorId,
    method: 'get'
  })
}

// 新增部门
export function addDept(data) {
  return request({
    url: '/system/floor',
    method: 'post',
    data: data
  })
}

// 修改部门
export function updateDept(data) {
  return request({
    url: '/system/floor',
    method: 'put',
    data: data
  })
}

// 删除部门
export function delDept(floorId) {
  return request({
    url: '/system/floor/' + floorId,
    method: 'delete'
  })
}


// 根据菜单编号获取详细信息
export function monitorReportStatistics(floorId) {
  return request({
    url: '/system/floor/monitorReportStatistics/?floorId=' + floorId,
    method: 'get'
  })
}