<style scoped>
.nightingale_chart_main {
    width: 20vw;
    height: 24.4vh;
}
</style>

<template>
    <div class="nightingale_chart_main">
        <div ref="nightingaleChart" style="width: 20vw; height: 24.4vh"></div>
    </div>
</template>

<script>
import { getServer } from '@/api/monitor/server';

export default {
    components: {},
    created() {
        this.init();
    },
    methods: {
        init() {
            this.getServer();
        },
        /** 查询服务器信息 */
        getServer() {
            getServer().then((response) => {
                this.server = response.data;
                this.$nextTick(() => {
                    this.drawPieCpu();
                });
            });
        },
        drawPieCpu() {
            // 基于准备好的dom，初始化echarts实例
            let myChart = this.$echarts.init(this.$refs.nightingaleChart);
            var option;

            option = {
                legend: {
                    orient: 'vertical',
                    x: 'right',
                    y: 'center',
                    left: '4%',
                    top: '8%',
                    show: true,
                    itemGap: 6,
                    textStyle: {
                        color: '#FFFFFF',
                    },
                },
                tooltip: {
                    axisPointer: {
                        crossStyle: {
                            color: '#ecf5ff',
                        },
                    },
                },
                toolbox: {
                    show: false,
                    feature: {
                        mark: { show: true },
                        dataView: { show: true, readOnly: false },
                        restore: { show: false },
                        saveAsImage: { show: false },
                    },
                },
                series: [
                    {
                        name: '电力监测',
                        type: 'pie',
                        radius: [18, 90],
                        center: ['65%', '50%'],
                        roseType: 'area',
                        itemStyle: {
                            borderRadius: 8,
                        },
                        label: {
                            show: true,
                        },
                        emphasis: {
                            label: {
                                show: true,
                            },
                        },
                        data: [
                            { value: 40, name: '今日接收' },
                            { value: 38, name: '今日发送' },
                            { value: 32, name: '订阅总数' },
                            { value: 30, name: '发布消息' },
                            { value: 28, name: '接收消息' },
                            { value: 26, name: '认证次数' },
                            { value: 22, name: '认证成功' },
                        ],
                        label: {
                            normal: {
                                show: true,
                                formatter: '{b}: {c} ({d}%)', // 显示名称、数值和百分比
                                position: 'inside', // 可根据需要调整标签位置
                            },
                        },
                    },
                ],
            };
            option && myChart.setOption(option);
        },
    },
};
</script>
