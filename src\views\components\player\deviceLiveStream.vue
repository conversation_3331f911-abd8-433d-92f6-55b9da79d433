<template>
  <div>
    <el-row>
      <span style="margin-left: 10px" prop="channelName">通道名称：</span>
      <el-select v-model="channelId" placeholder="请选择" @change="changeChannel()" size="small">
        <el-option v-for="option in channelList" :key="option.value" :label="option.label" :value="option.value"></el-option>
      </el-select>
      <span style="margin: 10px 10px 10px 30px">开启直播录像:</span>
      <el-switch v-model="playrecord" active-color="#13ce66" inactive-color="#c4c6c9" style="border-radius: 10px" :disabled="channelId === ''" @change="startPlayRecord"></el-switch>
    </el-row>
    <player ref="player" :playerinfo="playinfo" class="components-container"></player>
  </div>
</template>
<script>
import player from '@/views/components/player/player.vue';
import { startPlay, closeStream, listChannel } from '@/api/iot/channel';
import { startPlayRecord } from '@/api/iot/record';

export default {
  name: 'device-live-stream',
  components: {
    player,
  },
  props: {
    device: {
      type: Object,
      default: null,
    },
  },
  watch: {
    // 获取到父组件传递的device后
    device: function (newVal, oldVal) {
      this.deviceInfo = newVal;
      if (this.deviceInfo.channelId) {
        this.channelId = this.deviceInfo.channelId;
        this.changeChannel();
      }
      if (this.deviceInfo && this.deviceInfo.deviceId !== 0) {
        this.queryParams.deviceSipId = this.deviceInfo.serialNumber;
        this.deviceId = this.device.serialNumber;
      }
    },
  },
  data() {
    return {
      deviceInfo: {},
      deviceId: '',
      channelId: '',
      streamId: '',
      ssrc: '',
      playurl: '',
      playinfo: {},
      playrecord: false,
      playrecording: false,
      playing: false,
      channelList: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        deviceSipId: null,
        channelSipId: null,
      },
    };
  },
  created() {
    this.queryParams.deviceSipId = this.device.serialNumber;
    this.deviceId = this.device.serialNumber;
    this.getList();
    this.playinfo = {
      playtype: 'play',
      deviceId: this.device.serialNumber,
    };
  },
  beforeDestroy() {},
  destroyed() {
    this.closeStream();
  },
  methods: {
    /** 查询监控设备通道信息列表 */
    getList() {
      this.loading = true;
      listChannel(this.queryParams).then((response) => {
        this.channelList = response.rows.map((item) => {
          return { value: item.channelSipId, label: item.channelName };
        });
        console.log(this.channelList);
      });
    },
    changeChannel() {
      this.playinfo.channelId = this.channelId;
      this.sendDevicePush();
    },
    // 直播播放
    sendDevicePush() {
      if (!this.channelId) {
        console.log('直播通道: [' + this.channelId + ']');
        return;
      }
      startPlay(this.deviceId, this.channelId).then((response) => {
        const res = response.data;
        this.streamId = res.streamId;
        this.playurl = res.playurl;
        if (!this.$refs.player.isInit) {
          this.$refs.player.init();
        }
        this.$refs.player.play(res.playurl);
        this.playing = true;
      });
    },
    // 开启直播录像功能
    startPlayRecord() {
      this.deviceId = this.queryParams.deviceSipId;
      if (!this.channelId) {
        console.log('直播录像通道: [' + this.channelId + ']');
        return;
      }
      if (this.playrecord) {
        this.closeStream();
        startPlayRecord(this.deviceId, this.channelId).then((response) => {
          console.log('开始录像：' + this.deviceId + ' : ' + this.channelId);
          this.playrecording = true;
          const res = response.data;
          this.streamId = res.streamId;
          this.playurl = res.playurl;
          if (!this.$refs.player.isInit) {
            this.$refs.player.init();
          }
          this.$refs.player.play(res.playurl);
          this.playing = true;
        });
      } else {
        this.playrecording = false;
        this.closeStream();
        this.sendDevicePush();
      }
    },
    closeStream() {
      if (this.playrecording === true) {
        return;
      }
      if (this.playing && this.streamId) {
        closeStream(this.deviceId, this.streamId).then((res) => {
          this.streamId = '';
          this.ssrc = '';
          this.playurl = '';
        });
        this.playing = false;
        // this.$refs.player.destroy();
      }
    },
    destroy() {
      if (this.playing && this.streamId) {
        this.$refs.player.destroy();
      }
    },
  },
};
</script>
