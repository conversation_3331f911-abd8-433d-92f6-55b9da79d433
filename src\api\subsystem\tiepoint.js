import request from '@/utils/request';
import { parseStrEmpty } from '@/utils/ruoyi';

export function findTiePointByfloorIdAndCategoryName(floorId,categoryName) {
    return request({
        url: '/system/tiePoint/findTiePointByfloorIdAndCategoryName?floorId='+floorId+"&categoryName="+categoryName,
        method: 'get'
    })
}

// 新增楼层分布图上绑点子系统图标
export function addTiePoint(data) {
    return request({
        url: '/system/tiePoint/',
        method: 'post',
        data: data,
    });
}

// 更新楼层分布图上绑点子系统图标
export function updateTiePoint(tiePointId, dragStartX,dragStartY) {
    return request({
        url: '/system/tiePoint/updateTiePoint?tiePointId=' + tiePointId + '&dragStartX=' + dragStartX + '&dragStartY=' + dragStartY,
        method: 'put',
    });
}


// 删除楼层分布图上绑点子系统图标
export function deleteTiePoint(tiePointId) {
    return request({
      url: '/system/tiePoint/deleteTiePoint?tiePointId=' + tiePointId,
      method: 'delete'
    })
}

// 查询子系统图标是否已经存库
export function getTiePointByTiePointId(tiePointId) {
    return request({
      url: '/system/tiePoint/' + tiePointId,
      method: 'get'
    })
}
  