<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="设备编号" prop="serialNumber">
        <el-input v-model="queryParams.serialNumber" placeholder="请输入设备编号" clearable
          @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="产品名称" prop="productName">
        <el-input v-model="queryParams.productName" placeholder="请输入产品名称" clearable @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="场景名称" prop="scenarioName">
        <el-input v-model="queryParams.scenarioName" placeholder="请输入场景名称" clearable
          @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="handleAdd"
          v-hasPermi="['iot:scenarioDevice:add']">新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="success" plain icon="el-icon-edit" size="mini" :disabled="single" @click="handleUpdate"
          v-hasPermi="['iot:scenarioDevice:edit']">修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="danger" plain icon="el-icon-delete" size="mini" :disabled="multiple" @click="handleDelete"
          v-hasPermi="['iot:scenarioDevice:remove']">删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="warning" plain icon="el-icon-download" size="mini" @click="handleExport"
          v-hasPermi="['iot:scenarioDevice:export']">导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="deviceList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="ID" align="center" prop="id" />
      <el-table-column label="设备编号" align="center" prop="serialNumber" />
      <el-table-column label="产品名称" align="center" prop="productName" />
      <el-table-column label="场景" align="center" prop="scenarioName" />
      <!--      <el-table-column label="状态" align="center" prop="status" />-->
      <el-table-column label="备注" align="center" prop="remark" />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button size="mini" type="text" icon="el-icon-edit" @click="handleUpdate(scope.row)"
            v-hasPermi="['iot:scenarioDevice:edit']">修改</el-button>
          <el-button size="mini" type="text" icon="el-icon-delete" @click="handleDelete(scope.row)"
            v-hasPermi="['iot:scenarioDevice:remove']">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize"
      @pagination="getList" />

    <!-- 添加或修改场景设备对话框 -->
    <el-dialog :close-on-click-modal="false" :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="所属产品" prop="productId">
          <el-select v-model="form.productId" placeholder="请选择产品" @change="selectProduct">
            <el-option v-for="product in productShortList" :key="product.id" :label="product.name"
              :value="product.id"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="设备" prop="serialNumber">
          <el-select v-model="form.serialNumber" placeholder="请选择设备" @change="selectDevice">
            <el-option v-for="device in deviceShortList" :key="device.serialNumber" :label="device.deviceName"
              :value="device.serialNumber"></el-option>
          </el-select>
        </el-form-item>
        <!--        <el-form-item label="设备" prop="serialNumber">-->
        <!--          <el-input readonly v-model="item.deviceCount" size="small" placeholder="请选择设备" style="margin-top: 3px">-->
        <!--            <span slot="prepend" disabled>数量</span>-->
        <!--            <el-button slot="append" @click="handleSelectDevice('trigger', item, index)" size="small">选择设备</el-button>-->
        <!--          </el-input>-->
        <!--        </el-form-item>-->
        <!--        <el-form-item label="产品ID" prop="productId">-->
        <!--          <el-input v-model="form.productId" placeholder="请输入产品ID" />-->
        <!--        </el-form-item>-->
        <!--        <el-form-item label="产品名称" prop="productName">-->
        <!--          <el-input v-model="form.productName" placeholder="请输入产品名称" />-->
        <!--        </el-form-item>-->
        <el-form-item label="场景" prop="scenarioId">
          <el-select v-model="form.scenarioId" placeholder="请选择场景" @change="selectScenario">
            <el-option v-for="scenario in scenarioShortList" :key="scenario.scenarioId" :label="scenario.scenarioName"
              :value="scenario.scenarioId"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" type="textarea" placeholder="请输入内容" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listScenarioDevice, getScenarioDevice, delScenarioDevice, addScenarioDevice, updateScenarioDevice } from "@/api/iot/scenarioDevice";
import { listShortProduct } from '@/api/iot/product';
import { listAllDevice } from "../../../api/iot/device";
import { listAllScenario } from "../../../api/iot/scenario";
// import deviceList from './scene/device-list';
export default {
  name: "Device",
  data() {
    return {
      // 产品列表
      productShortList: [],
      // 设备列表
      deviceShortList: [],
      // 场景列表
      scenarioShortList: [],
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 场景设备表格数据
      deviceList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        serialNumber: null,
        productId: null,
        productName: null,
        scenarioName: null,
        status: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        serialNumber: [
          { required: true, message: "设备编号不能为空", trigger: "blur" }
        ],
        productId: [
          { required: true, message: "产品ID不能为空", trigger: "blur" }
        ],
        productName: [
          { required: true, message: "产品名称不能为空", trigger: "blur" }
        ],
        scenarioId: [
          { required: true, message: "场景ID不能为空", trigger: "blur" }
        ],
      }
    };
  },
  created() {
    this.getList();
    this.getProductShortList();
    this.getDeviceShortList();
    this.getScenarioShortList();
  },
  methods: {
    /** 选择产品 */
    selectProduct(val) {
      for (var i = 0; i < this.productShortList.length; i++) {
        if (this.productShortList[i].id == val) {
          this.form.productName = this.productShortList[i].name;
          this.form.serialNumber = null;
          this.getDeviceShortList();
          return;
        }
      }
    },
    /** 查询产品简短列表 */
    getProductShortList() {
      listShortProduct().then((response) => {
        this.productShortList = response.data;
      });
    },
    /** 查询设备简短列表 */
    getDeviceShortList() {
      let params = {
      }
      params.productId = this.form.productId == 0 ? null : this.form.productId;
      listAllDevice(params).then((response) => {
        this.deviceShortList = response.data;
      });
    },
    /** 选择设备 */
    selectDevice(val) {
      for (var i = 0; i < this.deviceShortList.length; i++) {
        if (this.deviceShortList[i].id == val) {
          this.form.serialNumber = this.deviceShortList[i].name;
          return;
        }
      }
    },
    /** 查询场景简短列表 */
    getScenarioShortList() {
      listAllScenario().then((response) => {
        this.scenarioShortList = response.data;
      });
    },
    /** 选择场景 */
    selectScenario(val) {
      for (var i = 0; i < this.scenarioShortList.length; i++) {
        if (this.scenarioShortList[i].scenarioId == val) {
          this.form.scenarioId = this.scenarioShortList[i].scenarioName;
          return;
        }
      }
    },
    /** 查询场景设备列表 */
    getList() {
      this.loading = true;
      listScenarioDevice(this.queryParams).then(response => {
        this.deviceList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        serialNumber: null,
        productId: null,
        productName: null,
        scenarioId: null,
        status: null,
        delFlag: null,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null,
        remark: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加场景设备";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids
      getScenarioDevice(id).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改场景设备";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.id != null) {
            updateScenarioDevice(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addScenarioDevice(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal.confirm('是否确认删除场景设备编号为"' + ids + '"的数据项？').then(function () {
        return delScenarioDevice(ids);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => { });
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('iot/scenarioDevice/export', {
        ...this.queryParams
      }, `device_${new Date().getTime()}.xlsx`)
    }
  }
};
</script>
