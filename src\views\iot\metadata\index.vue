<template>
    <div class="app-container">
        <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
            <el-form-item label="名称" prop="name">
                <el-input v-model="queryParams.name" placeholder="请输入名称" clearable @keyup.enter.native="handleQuery" />
            </el-form-item>
            <el-form-item>
                <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
                <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
            </el-form-item>
        </el-form>

        <el-row :gutter="10" class="mb8">
            <el-col :span="1.5">
                <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="handleAdd" v-hasPermi="['iot:metadata:add']">新增</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="success" plain icon="el-icon-edit" size="mini" :disabled="single" @click="handleUpdate" v-hasPermi="['iot:metadata:edit']">修改</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="danger" plain icon="el-icon-delete" size="mini" :disabled="multiple" @click="handleDelete" v-hasPermi="['iot:metadata:remove']">删除</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="warning" plain icon="el-icon-download" size="mini" @click="handleExport" v-hasPermi="['iot:metadata:export']">导出</el-button>
            </el-col>
            <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>

        <el-table v-loading="loading" :data="metadataList" @selection-change="handleSelectionChange">
            <el-table-column type="selection" width="55" align="center" />
            <el-table-column label="ID" align="center" prop="id" />
            <el-table-column label="名称" align="center" prop="name" />
            <el-table-column label="类型" align="center" prop="type">
                <template slot-scope="scope">
                    <dict-tag :options="dict.type.rule_script_action" :value="scope.row.type" size="small" />
                </template>
            </el-table-column>
            <el-table-column label="数据json" align="center" prop="dataJson" />
            <el-table-column label="是否生效" align="center" prop="enable">
                <template slot-scope="scope">
                    <dict-tag :options="dict.type.iot_is_enable" :value="scope.row.enable" size="small" />
                </template>
            </el-table-column>
            <el-table-column label="备注" align="center" prop="remark" />
            <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
                <template slot-scope="scope">
                    <el-button size="mini" type="text" icon="el-icon-edit" @click="handleUpdate(scope.row)" v-hasPermi="['iot:metadata:edit']">修改</el-button>
                    <el-button size="mini" type="text" icon="el-icon-delete" @click="handleDelete(scope.row)" v-hasPermi="['iot:metadata:remove']">删除</el-button>
                </template>
            </el-table-column>
        </el-table>

        <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize" @pagination="getList" />

        <!-- 添加或修改规则引擎元数据对话框 -->
        <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
            <el-form ref="form" :model="form" :rules="rules" label-width="80px">
                <el-form-item label="名称" prop="name">
                    <el-input v-model="form.name" placeholder="请输入名称" />
                </el-form-item>
                <el-form-item label="类型" prop="type">
                    <el-select v-model="form.type" placeholder="请选择脚本动作" @change="selectAction()" style="width: 100%">
                        <el-option v-for="dict in dict.type.rule_script_action" :key="dict.label" :label="dict.label" :value="Number(dict.value)"></el-option>
                    </el-select>
                </el-form-item>
                <!--        <el-form-item label="数据json" prop="dataJson">-->
                <!--          <el-input v-model="form.dataJson" type="textarea" placeholder="请输入内容" />-->
                <!--        </el-form-item>-->
                <el-form-item label="推送地址" prop="dataJson.url" v-if="form.type == 3">
                    <el-input v-model="form.dataJson.url" placeholder="请输入推送地址" />
                </el-form-item>
                <el-form-item label="推送地址" prop="dataJson.broker" v-if="form.type == 4">
                    <el-input v-model="form.dataJson.broker" placeholder="请输入推送地址" />
                </el-form-item>
                <el-form-item label="客户端id" prop="dataJson.clientId" v-if="form.type == 4">
                    <el-input v-model="form.dataJson.clientId" placeholder="请输入客户端id" />
                </el-form-item>
                <el-form-item label="用户名" prop="dataJson.username" v-if="form.type == 4">
                    <el-input v-model="form.dataJson.username" placeholder="请输入用户名" />
                </el-form-item>
                <el-form-item label="密码" prop="dataJson.password" v-if="form.type == 4">
                    <el-input v-model="form.dataJson.password" placeholder="请输入密码" />
                </el-form-item>
                <el-form-item label="主题" prop="dataJson.routeTopic" v-if="form.type == 4">
                    <el-input v-model="form.dataJson.routeTopic" placeholder="请输入主题" />
                </el-form-item>
                <el-form-item label="消息质量" prop="dataJson.qos" v-if="form.type == 4">
                    <el-input v-model="form.dataJson.qos" placeholder="请输入消息质量" />
                </el-form-item>
                <el-form-item label="推送地址" prop="dataJson.url" v-if="form.type == 6">
                    <el-input v-model="form.dataJson.url" placeholder="请输入推送地址" />
                </el-form-item>
                <el-form-item label="主题" prop="dataJson.tempTopic" v-if="form.type == 6">
                    <el-input v-model="form.dataJson.tempTopic" placeholder="请输入主题" />
                </el-form-item>
                <el-form-item label="定时表达式" prop="dataJson.cron" v-if="form.type == 6">
                    <el-input v-model="form.dataJson.cron" placeholder="请输入定时表达式" />
                </el-form-item>
                <!--        <el-form-item label="删除标志" prop="delFlag">-->
                <!--          <el-input v-model="form.delFlag" placeholder="请输入删除标志" />-->
                <!--        </el-form-item>-->
                <el-form-item label="备注" prop="remark">
                    <el-input v-model="form.remark" type="textarea" placeholder="请输入内容" />
                </el-form-item>
            </el-form>
            <div slot="footer" class="dialog-footer">
                <el-button type="primary" @click="submitForm">确 定</el-button>
                <el-button @click="cancel">取 消</el-button>
            </div>
        </el-dialog>
    </div>
</template>

<script>
import { listMetadata, getMetadata, delMetadata, addMetadata, updateMetadata } from '@/api/iot/metadata';

export default {
    name: 'Metadata',
    dicts: ['rule_script_action', 'iot_is_enable'],
    data() {
        return {
            // 遮罩层
            loading: true,
            // 选中数组
            ids: [],
            // 非单个禁用
            single: true,
            // 非多个禁用
            multiple: true,
            // 显示搜索条件
            showSearch: true,
            // 总条数
            total: 0,
            // 规则引擎元数据表格数据
            metadataList: [],
            // 弹出层标题
            title: '',
            // 是否显示弹出层
            open: false,
            // 查询参数
            queryParams: {
                pageNum: 1,
                pageSize: 10,
                name: null,
                dataJson: null,
                enable: null,
            },
            // 表单参数
            form: {
                id: null,
                type: null,

                enable: null,
                delFlag: null,
                createBy: null,
                createTime: null,
                updateBy: null,
                updateTime: null,
                remark: null,
                dataJson: {
                    url: '',
                    broker: '',
                    clientId: '',
                    username: '',
                    password: '',
                    routeTopic: '',
                    qos: null,
                    tempTopic: '',
                    cron: '',
                },
                // httpJson
                httpJson: {
                    url: '',
                },
                // mqttJson
                mqttJson: {
                    broker: '',
                    clientId: '',
                    username: '',
                    password: '',
                    routeTopic: '',
                    qos: 0,
                },
                // timePushJson
                timePushJson: {
                    url: '',
                    tempTopic: '',
                    cron: '',
                },
            },
            // 表单校验
            rules: {
                name: [{ required: true, message: '名称不能为空', trigger: 'blur' }],
                type: [{ required: true, message: '类型不能为空', trigger: 'blur' }],
                // 修改: 校验规则绑定到 form.dataJson 的对应字段
                dataJson: {
                    url: [{ required: true, message: '推送地址不能为空', trigger: 'blur' }],
                    broker: [{ required: true, message: '推送地址不能为空', trigger: 'blur' }],
                    clientId: [{ required: true, message: '客户端id不能为空', trigger: 'blur' }],
                    username: [{ required: true, message: '用户名不能为空', trigger: 'blur' }],
                    password: [{ required: true, message: '密码不能为空', trigger: 'blur' }],
                    routeTopic: [{ required: true, message: '主题不能为空', trigger: 'blur' }],
                    qos: [{ required: true, message: '消息质量不能为空', trigger: 'blur' }],
                    tempTopic: [{ required: true, message: '主题不能为空', trigger: 'blur' }],
                    cron: [{ required: true, message: '定时表达式不能为空', trigger: 'blur' }],
                },
            },
        };
    },
    created() {
        this.getList();
    },
    methods: {
        /** 查询规则引擎元数据列表 */
        getList() {
            this.loading = true;
            listMetadata(this.queryParams).then((response) => {
                this.metadataList = response.rows;
                this.total = response.total;
                this.loading = false;
            });
        },
        // 取消按钮
        cancel() {
            this.open = false;
            this.reset();
        },
        // 表单重置
        reset() {
            this.form = {
                id: null,
                type: null,
                dataJson: null,
                enable: null,
                delFlag: null,
                createBy: null,
                createTime: null,
                updateBy: null,
                updateTime: null,
                remark: null,
                // httpJson
                httpJson: {
                    url: '',
                },
                // mqttJson
                mqttJson: {
                    broker: '',
                    clientId: '',
                    username: '',
                    password: '',
                    routeTopic: '',
                    qos: 0,
                },
                // timePushJson
                timePushJson: {
                    url: '',
                    tempTopic: '',
                    cron: '',
                },
            };
            this.resetForm('form');
        },
        /** 搜索按钮操作 */
        handleQuery() {
            this.queryParams.pageNum = 1;
            this.getList();
        },
        /** 重置按钮操作 */
        resetQuery() {
            this.resetForm('queryForm');
            this.handleQuery();
        },
        // 多选框选中数据
        handleSelectionChange(selection) {
            this.ids = selection.map((item) => item.id);
            this.single = selection.length !== 1;
            this.multiple = !selection.length;
        },
        /**选择产品 */
        selectAction() {
            if (3 === this.form.type) {
                // HTTP
                this.form.dataJson = this.form.httpJson;
            }
            if (4 === this.form.type) {
                // MQTT
                this.form.dataJson = this.form.mqttJson;
            }
            if (6 === this.form.type) {
                // 定时推送
                this.form.dataJson = this.form.timePushJson;
            }
        },
        /** 新增按钮操作 */
        handleAdd() {
            this.reset();
            this.open = true;
            this.title = '添加规则引擎元数据';
        },
        /** 修改按钮操作 */
        handleUpdate(row) {
            this.reset();
            const id = row.id || this.ids;
            getMetadata(id).then((response) => {
                this.form = response.data;
                this.form.dataJson = JSON.parse(this.form.dataJson);
                // if (3 === this.form.type) {
                //   // HTTP
                //   this.form.httpJson = JSON.parse(this.form.dataJson);
                // }
                // if (4 === this.form.type) {
                //   // MQTT
                //   this.form.mqttJson = JSON.parse(this.form.dataJson);
                // }
                // if (6 === this.form.type) {
                //   // 定时推送
                //   this.form.timePushJson = JSON.parse(this.form.dataJson);
                // }
                // 修改: 确保 dataJson 中的字段被正确验证
                if (this.form.type == 3 && !this.form.dataJson.url) {
                    this.$message.error('推送地址不能为空');
                    return;
                }
                if (
                    this.form.type == 4 &&
                    (!this.form.dataJson.broker || !this.form.dataJson.clientId || !this.form.dataJson.username || !this.form.dataJson.password || !this.form.dataJson.routeTopic || this.form.dataJson.qos === null)
                ) {
                    this.$message.error('MQTT相关字段不能为空');
                    return;
                }
                if (this.form.type == 6 && (!this.form.dataJson.url || !this.form.dataJson.tempTopic || !this.form.dataJson.cron)) {
                    this.$message.error('定时推送相关字段不能为空');
                    return;
                }
                this.open = true;
                this.title = '修改规则引擎元数据';
            });
        },
        /** 提交按钮 */
        submitForm() {
            this.$refs['form'].validate((valid) => {
                if (valid) {
                    // if (3 === this.form.type) {
                    //   // HTTP
                    //   this.form.dataJson = JSON.stringify(this.form.httpJson);
                    // }
                    // if (4 === this.form.type) {
                    //   // MQTT
                    //   this.form.dataJson = JSON.stringify(this.form.mqttJson);
                    // }
                    // if (6 === this.form.type) {
                    //   // 定时推送
                    //   this.form.dataJson = JSON.stringify(this.form.timePushJson);
                    // }
                    this.form.dataJson = JSON.stringify(this.form.dataJson);
                    if (this.form.id != null) {
                        updateMetadata(this.form).then((response) => {
                            this.$modal.msgSuccess('修改成功');
                            this.open = false;
                            this.getList();
                        });
                    } else {
                        addMetadata(this.form).then((response) => {
                            this.$modal.msgSuccess('新增成功');
                            this.open = false;
                            this.getList();
                        });
                    }
                }
            });
        },
        /** 删除按钮操作 */
        handleDelete(row) {
            const ids = row.id || this.ids;
            this.$modal
                .confirm('是否确认删除规则引擎元数据编号为"' + ids + '"的数据项？')
                .then(function () {
                    return delMetadata(ids);
                })
                .then(() => {
                    this.getList();
                    this.$modal.msgSuccess('删除成功');
                })
                .catch(() => {});
        },
        /** 导出按钮操作 */
        handleExport() {
            this.download(
                'iot/metadata/export',
                {
                    ...this.queryParams,
                },
                `metadata_${new Date().getTime()}.xlsx`
            );
        },
    },
};
</script>
