<template>
    <div>
        <el-form :model="queryParams" ref="queryForm" :inline="true" label-width="68px" style="margin-bottom: -20px">
            <el-form-item label="设备编号" prop="serialNumber">
                <el-input v-model="queryParams.serialNumber" placeholder="请输入设备编号" @blur="handleChange" clearable size="small" @keyup.enter.native="handleQuery" />
                <!-- <el-select v-model="queryParams.serialNumber" filterable placeholder="请选择" @change="handleChange">
                    <el-option v-for="item in serialNumberdata" :key="item.serialNumber" :label="item.serialNumber" :value="item.serialNumber"></el-option>
                </el-select> -->
            </el-form-item>
            <el-form-item label="标识符" prop="identity">
                <!-- <el-input v-model="queryParams.identity" placeholder="请输入标识符" clearable size="small" @keyup.enter.native="handleQuery" /> -->
                <el-select v-model="queryParams.identityList" multiple collapse-tags placeholder="请选择标识符">
                    <el-option v-for="item in identitydata" :key="item.identifier" :label="item.modelName" :value="item.identifier"></el-option>
                </el-select>
            </el-form-item>
            <el-form-item label="时间范围" prop="timeRange">
                <el-date-picker
                    v-model="dateRange"
                    size="small"
                    style="width: 340px"
                    value-format="yyyy-MM-dd HH:mm:ss"
                    type="datetimerange"
                    range-separator="-"
                    start-placeholder="开始日期"
                    end-placeholder="结束日期"
                ></el-date-picker>
            </el-form-item>
            <el-form-item>
                <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
                <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
            </el-form-item>
        </el-form>
    </div>
</template>

<script>
import { getAllShortDevice, getDeviceModel } from '@/api/customize/histroy';
export default {
    name: 'SearchForm',
    props: {
        // 接收父组件传递的查询参数
        initQuery: {
            type: Object,
            default: () => ({}),
        },
    },
    data() {
        return {
            //编号列表
            serialNumberdata: [],
            //模型列表
            identitydata: [],
            // 日期范围
            dateRange: [],
            // 查询参数
            queryParams: {
                serialNumber: undefined,
                identityList: [],
                beginTime: undefined,
                endTime: undefined,
                modelNameList: [],
            },
        };
    },
    mounted() {
        this.$nextTick(() => {
            // // 确保 DOM 更新完成
            // getAllShortDevice().then((res) => {
            //     this.serialNumberdata = res.rows;
            // });
        });
    },
    watch: {
        initQuery: {
            handler(val) {
                // 当父组件传递的查询参数变化时，更新本地查询参数
                if (val) {
                    this.queryParams = { ...this.queryParams, ...val };
                }
            },
            immediate: true,
            deep: true,
        },
    },
    methods: {
        /** 搜索按钮操作 */
        handleQuery() {
            // 校验提交内容是否为空
            // if (!this.queryParams.serialNumber) {
            //     this.$message.error('设备编号不能为空');
            //     return;
            // }
            // if (this.queryParams.identityList.length === 0) {
            //     this.$message.error('标识符不能为空');
            //     return;
            // }
            // if (!this.dateRange || this.dateRange.length === 0) {
            //     this.$message.error('时间范围不能为空');
            //     return;
            // }

            // 处理日期范围
            let params = { ...this.queryParams };
            if (this.dateRange && this.dateRange.length > 0) {
                params.beginTime = this.dateRange[0];
                params.endTime = this.dateRange[1];
            } else {
                params.beginTime = undefined;
                params.endTime = undefined;
            }

            // 根据 identityList 查找对应的 modelName
            const modelNameList = this.queryParams.identityList.map((identifier) => {
                const item = this.identitydata.find((item) => item.identifier === identifier);
                return item ? item.modelName : null;
            });

            // 将 modelNameList 添加到参数中
            params.modelNameList = modelNameList;

            // 向父组件发送查询事件
            this.$emit('search', params);
        },
        /** 重置按钮操作 */
        resetQuery() {
            this.dateRange = [];
            this.resetForm('queryForm');
            this.queryParams.identityList = [];
            this.queryParams.modelNameList = [];
            this.$emit('reset');
        },
        /** 提供给父组件调用的重置方法 */
        reset() {
            this.dateRange = [];
            this.queryParams.identityList = [];
            this.queryParams.modelNameList = [];
            this.resetForm('queryForm');
        },
        handleChange(value) {
            console.log('this.queryParams.serialNumber :', this.queryParams.serialNumber);
            if (!this.queryParams.serialNumber) {
                this.queryParams.identityList = [];
                this.identitydata = [];
                return;
            }
            console.log('选中的值:', this.queryParams.serialNumber);
            this.identitydata = [];
            this.queryParams.identityList = [];
            // 这里可以执行选中后的逻辑
            getDeviceModel(this.queryParams.serialNumber).then((res) => {
                this.identitydata = res.data;
            });
        },
    },
};
</script>
