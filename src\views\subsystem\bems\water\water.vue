<template>
  <div class="app-container">
    <el-row :gutter="20">
      <!--公共菜单数据-->
      <el-col :span="4" :xs="24">
        <div class="head-container">
          <el-input v-model="deptName" placeholder="请输入菜单名称" clearable size="small" prefix-icon="el-icon-search"
            style="margin-bottom: 20px" />
        </div>
        <div class="head-container">
          <el-tree :data="deptOptions" :props="defaultProps" :expand-on-click-node="false"
            :filter-node-method="filterNode" ref="tree" node-key="id" default-expand-all highlight-current
            @node-click="handleNodeClick" />
        </div>
      </el-col>

      <!--用户数据-->
      <el-col :span="20">
        <div class="card-container">
          <el-card :body-style="{ width: '100%', padding: '6px 6px 6px 6px', height: '92vh' }">
            <!-- <div v-for="(image, index) in images" :key="index" class="draggable-image" >
                <el-image
                  :src="baseApi + image.resourceUrl"
                  :draggable="isDraggable" fit="cover"
                />
              </div> -->
            <el-empty style="margin: 200px;" description="暂无数据" v-if="geographyUrl == ''"></el-empty>
            <div class="draggable-image-container" v-show="geographyUrl != ''">
              <el-image class="geography_image" :src="baseApi + geographyUrl" fit="cover" />
              <div v-for="(item, index) in items" :key="index"
                :style="{ left: item.dragStartX + 'px', top: item.dragStartY + 'px' }" class="draggable-image">

                <el-popover placement="right-start" :width="260" trigger="hover">
                  <template #reference>
                    <img :src="baseApi + item.resourceUrl" alt="Draggable Image">
                  </template>

                  <el-row>
                    <el-col :span="24"
                      style="text-align: center;  margin-bottom: 8px; font-weight: 800;"><span>DDC-1#B1F-5生活水箱</span></el-col>
                    <el-col class="water-pump" :span="8"
                      style="text-align: center; height: 100px; border: 0px rgb(51.2, 126.4, 204) solid;">
                      <img style="width: 100%; height: 100%;"
                        :src="baseApi + '/profile/upload/2024/10/12/shuixiang_20241012182405A051.png'" alt="">
                    </el-col>
                    <el-col class="pilot-lamp" :span="16">
                      <span>当前液位：10.8<a>L</a></span>
                    </el-col>
                    <el-col class="pilot-lamp" :span="16">
                      <span>进水流量：30<a>m³/h</a></span>
                    </el-col>
                    <el-col class="pilot-lamp" :span="16">
                      <span>出水流量：30<a>m³/h</a></span>
                    </el-col>
                    <el-col class="pilot-lamp" :span="16">
                      <span>工作时间：168<a>H</a></span>
                    </el-col>
                  </el-row>
                </el-popover>
              </div>
            </div>
            <!-- <el-skeleton-item v-show="geographyUrl == ''" variant="image" description="暂无数据" style="width: 100%; height: 100%;" /> -->
            <!-- <el-empty description="暂无数据" v-if="geographyUrl == ''"></el-empty> -->
          </el-card>
        </div>
      </el-col>
    </el-row>

    <!-- 选择设备弹窗 -->
    <el-dialog :close-on-click-modal="false" :title="title" v-if="open" :visible.sync="open" center width="1051px">
      <!-- 组态 -->
      <!-- <CoolHubJson/> -->
    </el-dialog>
  </div>
</template>

<script>
import { listGallery } from '@/api/subsystem/gallery';//加载所有状态为 1正常 的图标
import { findTiePointDeviceByTiePointId } from "@/api/subsystem/tiepointDevice";//查询子系统的绑点关联关系
import { getDevice } from "@/api/iot/device";//查询设备详情
import { getDicts } from '@/api/system/dict/data';
import { deptTreeSelect, getGeography } from "@/api/subsystem/geography";
import { monitorReportStatistics } from "@/api/system/floor";
import { findTiePointByfloorIdAndCategoryName } from "@/api/subsystem/tiepoint";//查询绑定的点位
import Treeselect from "@riophae/vue-treeselect";
import "@riophae/vue-treeselect/dist/vue-treeselect.css";
import draggable from 'vuedraggable'
// import CoolHubJson from "./cool-hub-json";//组态JSON

export default {
  name: "User",
  dicts: ['subsystem_gallery_icon_type', 'sys_floor_type', 'iot_device_status'],
  components: {
    Treeselect,
    draggable,
    // CoolHubJson
  },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 根路径
      baseApi: process.env.VUE_APP_BASE_API,
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      galleryList: [],  // 图库管理表格数据
      geographyUrl: "", // 楼层CAD背景图

      // 部门树选项
      deptOptions: undefined,
      // 部门名称
      deptName: undefined,
      // 日期范围
      dateRange: [],
      // 岗位选项
      postOptions: [],
      // 角色选项
      roleOptions: [],
      defaultProps: {
        children: "children",
        label: "label"
      },
      //模块类型查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 20,
        categoryName: '',
        galleryName: '',
        resourceUrl: null,
        status: null,
        isAsc: 'desc',
      },
      queryGeographyParams: {
        floorId: undefined,
        galleryId: undefined,
        dragStartX: 0,
        dragStartY: 0
      },
      items: [],
      draggingIndex: null,
      dragStartAxleX: 0,
      dragStartAxleY: 0,
      dragGaTiepointId: undefined,
      monitorFloorId: 0,//选中的菜单编号
      monitorItems: [],
      monitorInfo: [],
      videoUrl: [''],
      spilt: 1, //分屏
      playerIdx: 0, //激活播放器
      updateLooper: 0, //数据刷新轮训标志
      count: 15,
      total: 0,
      loading: false,
    };
  },
  watch: {
    // 根据名称筛选部门树
    deptName(val) {
      this.$refs.tree.filter(val);
    }
  },
  created() {
    this.getDeptTree();//页面初始化查询左侧公共菜单
    // 获取数据
    this.getDatas();
    // this.getGeographyUrl();
    //查询视频监控统计报表
    this.getMonitorReportStatistics();
  },
  methods: {
    getMonitorReportStatistics() {
      if (this.queryGeographyParams.floorId != undefined) {
        this.monitorFloorId = this.queryGeographyParams.floorId;
      }
      monitorReportStatistics(this.monitorFloorId).then((response) => {
        if (response.code === 200) {
          this.monitorItems = response.data;
        }
      });
    },
    //双击事件
    handleDblClick(tiePointId) {
      //判断当前子系统图标在库中是否已经存在绑点的关联关系
      // findTiePointDeviceByTiePointId(tiePointId).then((response) => {
      //   if (response.code === 200) {
      //     if(response.data == undefined){//说明没有关联关系
      //       this.$modal.msgError("暂未绑点监控设备！");
      //     }else{
      //       getDevice(response.data.deviceId).then((response) => {
      //         this.monitorInfo = response.data;
      //         this.title = "【设备：" + response.data.deviceName + "】";
      //         //通知设备上传媒体流
      //         this.sendDevicePush(response.data);
      //       })
      //       this.open = true;
      //     }
      //   }
      // })
      this.title = "【AUF-1F-01】";
      this.open = true;
    },
    //-----------------------------------------------------------------------------------------------------------------------------------------------------------
    //获取子系统图标
    async getDatas() {
      //const dictType = 'subsystem_gallery_icon_type';
      //const { data } = (await this.getCategoryTypes(dictType)) || [];
      this.queryParams.categoryName = '生活水系统';
      this.queryParams.status = "1";
      this.getGalleryList();
    },
    // 获取种类
    getCategoryTypes(dictType) {
      return new Promise((resolve, reject) => {
        getDicts(dictType)
          .then((res) => {
            resolve(res);
          })
          .catch((error) => {
            reject(error);
          });
      });
    },
    getGalleryList() {
      this.loading = true;
      listGallery(this.queryParams).then((response) => {
        if (response.code === 200) {
          this.galleryList = response.rows;
          this.total = response.total;
        }
        this.loading = false;
      });
    },
    //模块类型选择
    handleGalleryChange(value) {
      this.queryParams.pageNum = 1;
      this.queryParams.categoryName = value;
      this.queryParams.status = "1";
      this.single = true;
      this.multiple = true;
      this.getGalleryList();
      this.queryCoordinate();
    },


    //-----------------------------------------------------------------------------------------------------------------------------------------------------------
    /** 查询部门下拉树结构 */
    getDeptTree() {
      deptTreeSelect().then(response => {
        this.deptOptions = response.data;
        this.queryGeographyParams.floorId = this.deptOptions[0].id;//获取左侧tree的根节点传给右侧查询楼层cad背景图
        this.getGeographyUrl();
      });
    },
    // 节点单击事件
    handleNodeClick(data) {
      this.queryGeographyParams.floorId = data.id;
      this.getGeographyUrl();
      this.getMonitorReportStatistics();
    },

    /** 查询楼层CAD背景图 */
    getGeographyUrl() {
      this.loading = true;
      getGeography(this.queryGeographyParams.floorId).then(response => {
        //判断查询菜单是否设置了背景图
        if (response.data == undefined) {
          this.geographyUrl = ""
        } else {
          this.geographyUrl = response.data.geographyUrl;
          this.queryCoordinate();
        }
      });
    },
    queryCoordinate() {
      //alert("根据该节点编号查询关于他的所有子系统图标===="+floorId);
      findTiePointByfloorIdAndCategoryName(this.queryGeographyParams.floorId, this.queryParams.categoryName).then(response => {
        this.items = response.data;
      });
    },
    // 筛选节点
    filterNode(value, data) {
      if (!value) return true;
      return data.label.indexOf(value) !== -1;
    },
  }
};
</script>

<style scoped>
.grid-content {
  height: 5.5vh;
  margin-top: 1vh;
}

.grid-content span {
  color: #E5EFF0;
  line-height: 5.5vh;
  margin-left: 1vw;
}

.grid-content span a {
  font-size: 20px;
  font-weight: 800;
}

.btn {
  margin: 0 10px;
}

.btn:hover {
  color: #409eff;
}

.btn.active {
  color: #409eff;
}

.redborder {
  border: 2px solid red !important;
}

.play-box {
  background-color: #000000;
  border: 1px solid #505050;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 0px;
  position: relative;
  border-radius: 5px;
}

.player-wrap {
  position: absolute;
  top: 0px;
  height: 100% !important;
  width: 100% !important;
}

/* ---------------------------------------------------------------------------------------------------------------------------------------------- */

::v-deep .el-dialog {
  /* background: linear-gradient(135deg, rgba(3, 51, 241, 0.114) 45%, rgba(34, 32, 34, 0.2) 55%); */
  background: #201f1f;
  border-radius: 8px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  background-image: url('../../../../assets/images/model/faHuModel.jpg');
  background-size: cover;
}

::v-deep .el-dialog__title {
  color: #FFFFFF;
  font-weight: 800;
  font-size: 20px;
}

::v-deep .el-dialog__headerbtn .el-dialog__close {
  color: #FFFFFF;
}

::v-deep .el-dialog__body {
  padding: 8px 14px 14px 14px;
  color: #606266;
  font-size: 14px;
  word-break: break-all;
}

.el-descriptions img {
  width: 30px;
  height: 30px;
  position: absolute;
  margin-top: 1px;
}

.el-descriptions .el-tag {
  font-size: 14px;
  margin: 2px 0 0 34px;
}

.el-descriptions .mx-1 {
  font-size: 14px;
  font-weight: 700;
  margin-left: 4px;
  color: #888888;
}

:deep(.my-label) {
  background: var(--el-color-success-light-9) !important;
}

:deep(.my-content) {
  background: var(--el-color-danger-light-9);
}

.demo_image_preview {
  display: flex;
  /* 使div变为Flex容器 */
  flex-wrap: nowrap;
  /* 避免图片换行 */
}

.draggable-image-container {
  position: relative;
}

.geography_image {
  /** 楼层背景样式 */
  width: 100%;
  height: 90.5vh;
  z-index: 1;
  display: block;
}

.draggable-image {
  position: absolute;
  cursor: pointer;
  z-index: 999;
}

.draggable-image img {
  width: 40px;
  height: 45px;
}

.pilot-lamp span {
  width: 100%;
  line-height: 24px;
  text-align: left;
  margin-left: 14px;
}

.pilot-lamp span a {
  font-size: 16px;
}
</style>
