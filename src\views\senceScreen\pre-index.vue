<template>
    <div class="pre-index">
        <h1 style="text-align: center; justify-content: center">请选择场景</h1>
        <el-carousel :interval="5000" type="card" @change="onCarouselChange">
            <el-carousel-item v-for="(scene, index) in scenes" :key="index">
                <div class="scene-card" @click="selectScene(scene)">
                    <img :src="scene.image" :alt="scene.name" />
                    <h2>{{ scene.name }}</h2>
                    <p>{{ scene.description }}</p>
                </div>
            </el-carousel-item>
        </el-carousel>
    </div>
</template>

<script>
export default {
    data() {
        return {
            scenes: [
                {
                    name: '桥梁监测',
                    image: require('@/assets/bigScreen/pre-sence/供水.jpg'),
                    description: '展示桥梁结构健康状态及实时监测数据',
                    route: '/bigScreen/bighome',
                },
                {
                    name: '供水监测',
                    image: require('@/assets/bigScreen/pre-sence/桥梁.jpg'),
                    description: '展示供水系统运行状态及水质监测数据',
                    route: '/bigScreen/bighome',
                },
                {
                    name: '燃气监测',
                    image: require('@/assets/bigScreen/pre-sence/燃气监测.jpg'),
                    description: '展示燃气管网运行状态及泄漏检测数据',
                    route: '/bigScreen/bighome',
                },
                {
                    name: '热力监测',
                    image: require('@/assets/bigScreen/pre-sence/热力.jpg'),
                    description: '展示燃气管网运行状态及泄漏检测数据',
                    route: '/bigScreen/bighome',
                },
                {
                    name: '排水监测',
                    image: require('@/assets/bigScreen/pre-sence/排水.jpg'),
                    description: '展示燃气管网运行状态及泄漏检测数据',
                    route: '/bigScreen/bighome',
                },
            ],
            currentScene: null,
        };
    },
    methods: {
        onCarouselChange(index) {
            this.currentScene = this.scenes[index];
        },
        selectScene(scene) {
            this.$router.push(scene.route);
        },
    },
};
</script>

<style scoped>
.pre-index {
    margin: 0 auto;
    padding: 20px;
    background-color: white; /* 设置背景颜色为白色 */
    text-align: center; /* 将“请选择场景”这五个字居中排列 */
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1); /* 添加阴影增加科技感 */
    height: 100vh; /* 设置高度为视口高度 */
}

.el-carousel {
    height: 100%; /* 设置轮播图高度为父容器高度 */
}

.el-carousel /deep/ .el-carousel__container {
    height: 800px !important;
}

/* 卡片式轮播自定义样式 */
.el-carousel--card .el-carousel__item {
    transition: all 0.5s ease;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.el-carousel--card .el-carousel__item.is-active {
    transform: scale(1.5);
}

.el-carousel--card .el-carousel__item:not(.is-active) {
    transform: scale(0.01);
    opacity: 0.4;
}

.scene-card {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
    cursor: pointer;
    transition: transform 0.3s;
    background: linear-gradient(145deg, #f7f7ff, #e7e9fc); /* 添加渐变背景增加科技感 */
    border-radius: 10px;
    padding: 20px;
}

.scene-card:hover {
    transform: scale(1.05);
}

.scene-card img {
    width: 100%;

    object-fit: cover;
    border-radius: 8px;
    margin-bottom: 10px;
}

.scene-card h2 {
    margin: 10px 0;
    font-size: 1.5em;
}

.scene-card p {
    font-size: 1em;
    color: #666;
    text-align: center;
}
</style>
