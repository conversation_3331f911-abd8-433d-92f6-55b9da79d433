#index {
    font-family: 'Avenir', Helvetica, Arial, sans-serif;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    height: 100%;
    width: 100%;
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    overflow: hidden;

    &.pageisScale {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        transform-origin: left top;
    }

    .host-body {
        height: 100%;

        .title_wrap {
            height: 8vh;
            width: 100%;
            background: url("../../assets/bigScreen/img/image.png");
            background-size: 100% 100%;
            text-align: center;
            z-index: 2;
            position: absolute;
            background-color: rgba(255, 255, 255, 0); /* 完全透明 */

            .weather{
                position: absolute;
                top: 3.4vh;
                display: flex;
                align-items: center;
                left: 2.2vw;
            }

            .timers {
                position: absolute;
                font-weight: 600;
                letter-spacing: 0.04vw;
                background: linear-gradient(92deg, #3179d0 0%, #00eaff 48.8525390625%, #01aaff 100%);
                -webkit-background-clip: text;
                -webkit-text-fill-color: transparent;
                right: 1.8vw;
                font-size: 0.88vw;
                top: 4.2vh;
                display: flex;
                align-items: center;
            }
        }

        .title {
            position: relative;
            text-align: center;
            background-size: cover;
            height: 8vh;
            line-height: 7.2vh;

            .title-text {
                font-size: 1.3vw;
                color: #ffffff;
                font-weight: 600;
                letter-spacing: 2px;
                width: 100%;
            }
        }
    }
}

.index_home {
    height: 100vh;
    width: 100%;
}

.common-main{
    height: 100%;
    width: 100%;
    padding: 0; 
    margin: 0;
    z-index: 1;
    position:absolute;
    background-color: #03b9f1;
}





.setting {
    position: fixed;
    width: 100%;
    height: 100%;
    z-index: 999;
    top: 0;
    left: 0;

    .left_shu {
        color: #000;
        font-weight: 900;
        position: relative;
        text-indent: 10px;
        padding:16px 0 10px 0 ;
        &::before {
            display: block;
            content: " ";
            height: 16px;
            width: 4px;
            border-radius: 2px;
            position: absolute;
            left: 0px;
        }
    }

    .setting_dislog {
        background-color: rgba($color: #000000, $alpha: .5);
        position: absolute;
        width: 100%;
        height: 100%;
        z-index: 0;
        right: 0;
        top: 0;
    }

    .setting_inner {
        box-sizing: border-box;
        background: #FFF;
        width: 340px;
        height: 100%;
        position: absolute;
        right: 0px;
        top: 0;
        z-index: 1;
        color: #000000;
        box-shadow: 0 8px 10px -5px rgba(0, 0, 0, .2), 0 16px 24px 2px rgba(0, 0, 0, .14), 0 6px 30px 5px rgba(0, 0, 0, .12);

        .setting_header {
            font-size: 20px;
            color: rgb(0, 0, 0);
            font-weight: 900;
            text-align: center;
            line-height: 40px;
        }

        .setting_body {
            padding: 0px 16px;
            box-sizing: border-box;
            position: relative;
        }

        .setting_item {
            font-size: 14px;
            line-height: 1.5;

            // display: flex;
            .setting_label {
                color: #555454;
            }
            .setting_label_tip{
                font-size: 12px;
                color: #838282;
            }
        }
    }

    .setting_inner {
        animation: rtl-drawer-out .3s;
    }
}


.settingShow {
    .setting_inner {
        animation: rtl-drawer-in .3s 1ms;
    }

}

.yh-setting-fade-enter-active {
    animation: yh-setting-fade-in .3s;
}

.yh-setting-fade-leave-active {

    animation: yh-setting-fade-out .3s;

}

@keyframes yh-setting-fade-in {
    0% {
        opacity: 0;
    }

    100% {
        opacity: 1;
    }
}

@keyframes yh-setting-fade-out {
    0% {
        opacity: 1;

    }

    100% {
        opacity: 0;
    }
}


@keyframes rtl-drawer-in {
    0% {
        transform: translate(100%, 0)
    }

    100% {
        -webkit-transform: translate(0, 0);
        transform: translate(0, 0)
    }
}

@keyframes rtl-drawer-out {
    0% {
        transform: translate(0, 0)
    }

    100% {
        transform: translate(100%, 0)
    }
}
