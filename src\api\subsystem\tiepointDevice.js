import request from '@/utils/request';
import { parseStrEmpty } from '@/utils/ruoyi';

// 查询子系统图标是否已经绑点
export function findTiePointDeviceByTiePointId(tiePointId) {
    return request({
      url: '/system/tiePointDevice/' + tiePointId,
      method: 'get'
    })
}

// 新增子系统图标和设备关联关系
export function addTiePointDevice(tiePointId,deviceId) {
    return request({
        url: '/system/tiePointDevice/addTiePointDevice?tiePointId='+tiePointId+"&deviceId="+deviceId,
        method: 'post'
    });
}

// 更新子系统图标和设备关联关系
export function updateTiePointDevice(tiePointId,deviceId) {
    return request({
        url: '/system/tiePointDevice/updateTiePointDevice?tiePointId='+tiePointId+"&deviceId="+deviceId,
        method: 'put'
    });
}

// 删除绑定的子系统图标和设备的关联关系
export function deleteTiePointDeviceById(tiePointId) {
    return request({
        url: '/system/tiePointDevice/' + tiePointId,
        method: 'delete',
    });
}