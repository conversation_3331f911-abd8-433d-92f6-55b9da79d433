<template>
    <div style="padding: 10px; background-color: #f8f8f8">
        <el-row :gutter="20" style="margin: 10px 0px 20px 0px">
            <el-col :xs="24" :sm="24" :md="24" :lg="14" :xl="14">
                <!-- <div style="overflow: hidden; border: 1px solid #ccc">
                    <div ref="map" style="height: 650px"></div>
                </div> -->
                <div style="overflow: hidden; border: 1px solid #ccc; height: 752px">
                    <div id="mapContainer" style="height: 750px; width: 100%"></div>
                </div>
            </el-col>

            <el-col :xs="24" :sm="24" :md="24" :lg="10" :xl="10">
                <el-card shadow="none" style="">
                    <h3 style="font-weight: bold">
                        <i class="el-icon-s-data"></i>
                        设备统计
                    </h3>
                    <el-row :gutter="40" class="panel-group">
                        <el-col :span="12" class="card-panel-col">
                            <div class="card-panel">
                                <div class="card-panel-icon-wrapper icon-blue">
                                    <svg-icon icon-class="device" class-name="card-panel-icon" />
                                </div>
                                <div class="card-panel-description">
                                    <div class="card-panel-text">设备数量</div>
                                    <count-to :start-val="0" :end-val="deviceStatistic.deviceCount" :duration="3000" class="card-panel-num" />
                                </div>
                            </div>
                        </el-col>
                        <el-col :span="12" class="card-panel-col">
                            <div class="card-panel">
                                <div class="card-panel-icon-wrapper icon-red">
                                    <svg-icon icon-class="monitor-a" class-name="card-panel-icon" />
                                </div>
                                <div class="card-panel-description">
                                    <div class="card-panel-text">监测数据</div>
                                    <count-to :start-val="0" :end-val="deviceStatistic.monitorCount" :duration="3000" class="card-panel-num" />
                                </div>
                            </div>
                        </el-col>
                        <el-col :span="12" class="card-panel-col">
                            <div class="card-panel">
                                <div class="card-panel-icon-wrapper icon-blue">
                                    <svg-icon icon-class="model" class-name="card-panel-icon" />
                                </div>
                                <div class="card-panel-description">
                                    <div class="card-panel-text">产品数量</div>
                                    <count-to :start-val="0" :end-val="deviceStatistic.productCount" :duration="1000" class="card-panel-num" />
                                </div>
                            </div>
                        </el-col>
                        <el-col :span="12" class="card-panel-col">
                            <div class="card-panel">
                                <div class="card-panel-icon-wrapper icon-red">
                                    <svg-icon icon-class="alert" class-name="card-panel-icon" />
                                </div>
                                <div class="card-panel-description">
                                    <div class="card-panel-text">告警数量</div>
                                    <count-to :start-val="0" :end-val="deviceStatistic.alertCount" :duration="1000" class="card-panel-num" />
                                </div>
                            </div>
                        </el-col>
                        <el-col :span="12" class="card-panel-col">
                            <div class="card-panel">
                                <div class="card-panel-icon-wrapper icon-blue">
                                    <svg-icon icon-class="log-a" class-name="card-panel-icon" />
                                </div>
                                <div class="card-panel-description">
                                    <div class="card-panel-text">操作记录</div>
                                    <count-to :start-val="0" :end-val="deviceStatistic.functionCount" :duration="2000" class="card-panel-num" />
                                </div>
                            </div>
                        </el-col>
                        <el-col :span="12" class="card-panel-col">
                            <div class="card-panel">
                                <div class="card-panel-icon-wrapper icon-red">
                                    <svg-icon icon-class="event-a" class-name="card-panel-icon" />
                                </div>
                                <div class="card-panel-description">
                                    <div class="card-panel-text">上报事件</div>
                                    <count-to :start-val="0" :end-val="deviceStatistic.eventCount" :duration="2000" class="card-panel-num" />
                                </div>
                            </div>
                        </el-col>
                    </el-row>
                </el-card>
                <el-card shadow="none" style="margin-top: 22px; height: 302px">
                    <h3 style="font-weight: bold; margin-bottom: 10px">
                        <i class="el-icon-s-order"></i>
                        信息栏
                    </h3>
                    <div style="cursor: pointer; display: table; width: 100%; line-height: 36px" @click="openDetail(item.noticeId)" v-for="item in noticeList" :key="item.noticeId">
                        <div style="display: table-cell; padding-right: 10px">
                            <el-tag size="mini" effect="dark" type="warning" v-if="item.noticeType == 2">公告</el-tag>
                            <el-tag size="mini" effect="dark" v-else>信息</el-tag>
                            {{ item.noticeTitle }}
                        </div>
                        <div style="display: table-cell; width: 90px; font-size: 14px">
                            <i class="el-icon-time"></i>
                            {{ parseTime(item.createTime, '{y}-{m}-{d}') }}
                        </div>
                    </div>
                </el-card>
            </el-col>
        </el-row>

        <el-card shadow="none" style="margin: 10px 10px 20px 10px">
            <el-row :gutter="120" v-if="isAdmin">
                <el-col :xs="24" :sm="24" :md="24" :lg="8" :xl="8">
                    <div style="padding: 20px">
                        <div ref="pieCpu" style="height: 161px"></div>
                    </div>
                </el-col>
                <el-col :xs="24" :sm="24" :md="24" :lg="8" :xl="8">
                    <div style="padding: 20px">
                        <div ref="pieMemery" style="height: 161px"></div>
                    </div>
                </el-col>
                <el-col :xs="24" :sm="24" :md="24" :lg="8" :xl="8">
                    <div style="padding: 20px">
                        <div ref="pieDisk" style="height: 161px"></div>
                    </div>
                </el-col>
            </el-row>
        </el-card>

        <!-- <el-card shadow="none" style="margin: 10px 10px 60px 10px">
      <el-row :gutter="40">
        <el-col :xs="24" :sm="24" :md="10" :lg="10" :xl="10" style="padding: 40px">
          <div style="padding: 30px; margin: 20px 0; font-size: 14px">
            <div style="font-size: 28px; font-weight: bold; margin-bottom: 20px">seefy物联网平台</div>
            <div style="display: table; font-size: 14px; margin-bottom: 10px">
              <div style="display: table-cell; line-height: 22px"><b style="color: #67c23a; margin-right: 10px">开源版本采用AGPL-3.0许可协议，商用需要获得授权</b></div>
            </div>
            <div style="display: table; margin-bottom: 10px">
              <div style="width: 70px; font-weight: bold; display: table-cell">开源版本：</div>
              <div style="display: table-cell; line-height: 22px">可用于个人学习和使用，非商业用途</div>
            </div>
            <div style="display: table">
              <div style="width: 70px; font-weight: bold; display: table-cell">商业版本：</div>
              <div style="display: table-cell; line-height: 22px">
                商业用途，并提供所有源码，功能优先开源版本发布。
                <br />
                <el-link target="_blank" href="https://seefy.cn/doc/pages/sponsor">查看详情 >></el-link>
              </div>
            </div>
          </div>
          <div style="padding: 30px; font-size: 14px">
            <div style="float: left; width: 230px">
              <el-image style="width: 210px" :src="require('@/assets/images/code.jpg')"></el-image>
            </div>
            <div style="float: left">
              <div style="font-size: 18px; font-weight: bold; margin: 16px 0">微信扫一扫，查看小程序端</div>
              <div style="font-size: 14px; font-weight: bold; margin: 16px 0; color: #f56c6c">右侧是移动端H5版本演示</div>
              <div style="display: table; margin-bottom: 10px">
                <div style="width: 70px; font-weight: bold; display: table-cell">官方网站</div>
                <div style="display: table-cell">
                  <el-link target="_blank" href="https://seefy.cn/">www.seefy.cn</el-link>
                </div>
              </div>
              <div style="display: table; margin-bottom: 10px">
                <div style="width: 70px; font-weight: bold; display: table-cell">在线文档</div>
                <div style="display: table-cell">
                  <el-link target="_blank" href="https://seefy.cn/doc">www.seefy.cn/doc</el-link>
                </div>
              </div>
              <div style="display: table; margin-bottom: 10px">
                <div style="width: 70px; font-weight: bold; display: table-cell">联系作者</div>
                <div style="display: table-cell">
                  <span>QQ 164770707</span>
                </div>
              </div>
              <div style="display: table; margin-bottom: 15px">
                <div style="width: 70px; font-weight: bold; display: table-cell">系统源码</div>
                <div style="display: table-cell">
                  <el-link target="_blank" href="https://gitee.com/kerwincui/wumei-smart" type="danger">Gitee源码</el-link>
                  <el-link target="_blank" href="https://github.com/kerwincui/seefy" style="margin-left: 20px">Github源码</el-link>
                </div>
              </div>
            </div>
          </div>
        </el-col>
        <el-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12" style="padding: 40px">
          <div class="phone">
            <iframe src="https://seefy.cn/h5" id="iframe" frameborder="0" scrolling="auto" height="100%" width="100%" class="phone-container"></iframe>
          </div>
        </el-col>
      </el-row>
    </el-card> -->

        <!--通知公告详情 -->
        <el-dialog :close-on-click-modal="false" :title="notice.noticeTitle" :visible.sync="open" width="800px" append-to-body>
            <div style="margin-top: -20px; margin-bottom: 10px">
                <el-tag size="mini" effect="dark" type="warning" v-if="notice.noticeType == 2">公告</el-tag>
                <el-tag size="mini" effect="dark" v-else>信息</el-tag>
                <span style="margin-left: 20px">{{ notice.createTime }}</span>
            </div>
            <div v-loading="loading" class="content">
                <div v-html="notice.noticeContent"></div>
            </div>
            <div slot="footer" class="dialog-footer">
                <el-button type="primary" @click="closeDetail">关 闭</el-button>
            </div>
        </el-dialog>

        <!-- <div style="width: 100%; text-align: center; font-size: 14px; color: #666; line-height: 32px; margin-top: 150px">
      <span>
        Copyright © 2021-2022
        <a href="https://seefy.cn/" target="_blank">seefy</a>
        |
        <a href="https://seefy.cn/" target="_blank">新辉物联</a>
        | Apache License
      </span>
      <br />
      <span>
        项目文档
        <a href="https://seefy.cn/doc/" target="_blank">https://seefy.cn/doc/</a>
      </span>
    </div> -->
    </div>
</template>

<script>
import { getDeviceStatistic } from '@/api/iot/device';
import { listNotice, getNotice } from '@/api/system/notice';
import CountTo from 'vue-count-to';
import { loadBMap } from '@/utils/map.js';
//安装的是echarts完整包，里面包含百度地图扩展，路径为 echarts/extension/bmap/bmap.js，将其引入
//ECharts的百度地图扩展，可以在百度地图上展现点图，线图，热力图等可视化
require('echarts/extension/bmap/bmap');
import { getServer } from '@/api/monitor/server';
import { listAllDeviceShort } from '@/api/iot/device';
import { loadTMap } from '@/utils/tianditu.js';

export default {
    name: 'Index',
    components: {
        CountTo,
    },
    data() {
        return {
            // 遮罩层
            loading: true,
            // 是否显示弹出层
            open: false,
            // 信息列表
            noticeList: [],
            // 信息详情
            notice: {},
            // 是否为管理员
            isAdmin: false,
            // 设备列表
            deviceList: [],
            // 设备统计信息
            deviceStatistic: {},
            // 设备总数
            deviceCount: 0,
            // 版本号
            version: '3.8.0',
            // 服务器信息
            server: {
                jvm: {
                    name: '',
                    version: '',
                    startTime: '',
                    runTime: '',
                    used: '',
                    total: 100,
                },
                sys: {
                    computerName: '',
                    osName: '',
                    computerIp: '',
                    osArch: '',
                },
                cpu: {
                    cpuNum: 1,
                },
                mem: {
                    total: 2,
                },
            },
            tableData: [],
        };
    },
    mounted() {
        // 添加全局方法以供按钮调用
        window.viewDeviceDetail = (deviceId) => {
            this.viewDeviceDetail(deviceId);
        };
    },
    created() {
        this.init();
        this.getAllDevice();
        this.getNoticeList();
        this.getDeviceStatistic();
    },
    methods: {
        init() {
            if (this.$store.state.user.roles.indexOf('tenant') === -1 && this.$store.state.user.roles.indexOf('general') === -1) {
                this.isAdmin = true;
                this.getServer();
            }
        },
        //刷新iframe
        flushIframe() {
            var iframe = window.parent.document.getElementById('iframe');
            iframe.contentWindow.location.reload(true);
        },
        /** 查询设备统计信息 */
        getDeviceStatistic() {
            getDeviceStatistic().then((response) => {
                this.deviceStatistic = response.data;
            });
        },
        /** 查询公告列表 */
        getNoticeList() {
            let queryParams = {
                pageNum: 1,
                pageSize: 6,
            };
            listNotice(queryParams).then((response) => {
                this.noticeList = response.rows.splice(0, 6);
            });
        },
        // 打开信息详情
        openDetail(id) {
            this.open = true;
            this.loading = true;
            getNotice(id).then((response) => {
                this.notice = response.data;
                this.open = true;
                this.loading = false;
            });
        },
        // 取消按钮
        closeDetail() {
            this.title = '';
            this.open = false;
        },
        /**查询所有设备 */
        getAllDevice() {
            listAllDeviceShort(this.queryParams).then((response) => {
                this.deviceList = response.rows;
                // this.deviceCount = response.total;
                //this.initMap();
                loadTMap().then(() => {
                    // 初始化地图
                    this.initMap();
                });
            });
        },
        /**加载地图*/
        loadMap() {
            this.$nextTick(() => {
                loadBMap().then(() => {
                    this.getmap();
                });
            });
        },
        /** 查询服务器信息 */
        getServer() {
            getServer().then((response) => {
                this.server = response.data;
                this.tableData = [
                    {
                        server: '服务器名',
                        serverContent: this.server.sys.computerName,
                        java: 'Java名称',
                        javaContent: this.server.jvm.name,
                    },
                    {
                        server: '服务器IP',
                        serverContent: this.server.sys.computerIp,
                        java: '启动时间',
                        javaContent: this.server.jvm.startTime,
                    },
                    {
                        server: '操作系统',
                        serverContent: this.server.sys.osName,
                        java: 'Java版本',
                        javaContent: this.server.jvm.version,
                    },
                    {
                        server: '系统架构',
                        serverContent: this.server.sys.osArch,
                        java: '运行时长',
                        javaContent: this.server.jvm.runTime,
                    },
                    {
                        server: 'CPU核心',
                        serverContent: this.server.cpu.cpuNum,
                        java: '占用内存',
                        javaContent: this.server.jvm.used,
                    },
                    {
                        server: '内存大小',
                        serverContent: this.server.mem.total,
                        java: 'JVM总内存',
                        javaContent: this.server.jvm.total,
                    },
                ];
                this.$nextTick(() => {
                    this.drawPieCpu();
                    this.drawPieMemery();
                    this.drawPieDisk();
                });
            });
        },

        /** 地图 */
        getmap() {
            var myChart = this.$echarts.init(this.$refs.map);
            var option;

            // 单击事件
            myChart.on('click', (params) => {
                if (params.data.deviceId) {
                    this.$router.push({
                        path: '/iot/device-edit',
                        query: {
                            t: Date.now(),
                            deviceId: params.data.deviceId,
                        },
                    });
                }
            });

            // 格式化数据
            let convertData = function (data, status) {
                var res = [];
                for (var i = 0; i < data.length; i++) {
                    var geoCoord = [data[i].longitude, data[i].latitude];
                    if (geoCoord && data[i].status == status) {
                        res.push({
                            name: data[i].deviceName,
                            value: geoCoord,
                            serialNumber: data[i].serialNumber,
                            status: data[i].status,
                            isShadow: data[i].isShadow,
                            firmwareVersion: data[i].firmwareVersion,
                            networkAddress: data[i].networkAddress,
                            productName: data[i].productName,
                            activeTime: data[i].activeTime == null ? '' : data[i].activeTime,
                            deviceId: data[i].deviceId,
                            serialNumber: data[i].serialNumber,
                            locationWay: data[i].locationWay,
                        });
                    }
                }
                return res;
            };
            option = {
                title: {
                    text: '设备分布（在线数 ' + this.deviceList.filter((x) => x.status == 3).length + '）',
                    subtext: 'seefy open source iot platform',
                    sublink: 'https://iot.seefy.cn',
                    target: '_blank',
                    textStyle: {
                        color: '#333',
                        textBorderColor: '#fff',
                        textBorderWidth: 10,
                    },
                    top: 10,
                    left: 'center',
                },
                tooltip: {
                    trigger: 'item',
                    formatter: function (params) {
                        var htmlStr = '<div style="padding:5px;line-height:28px;">';
                        htmlStr += "设备名称： <span style='color:#409EFF'>" + params.data.name + '</span><br />';
                        htmlStr += '设备编号： ' + params.data.serialNumber + '<br />';
                        htmlStr += '设备状态： ';
                        if (params.data.status == 1) {
                            htmlStr += "<span style='color:#E6A23C'>未激活</span>" + '<br />';
                        } else if (params.data.status == 2) {
                            htmlStr += "<span style='color:#F56C6C'>禁用</span>" + '<br />';
                        } else if (params.data.status == 3) {
                            htmlStr += "<span style='color:#67C23A'>在线</span>" + '<br />';
                        } else if (params.data.status == 4) {
                            htmlStr += "<span style='color:#909399'>离线</span>" + '<br />';
                        }
                        if (params.data.isShadow == 1) {
                            htmlStr += '设备影子： ' + "<span style='color:#67C23A'>启用</span>" + '<br />';
                        } else {
                            htmlStr += '设备影子： ' + "<span style='color:#909399'>未启用</span>" + '<br />';
                        }
                        htmlStr += '产品名称： ' + params.data.productName + '<br />';
                        htmlStr += '固件版本： Version ' + params.data.firmwareVersion + '<br />';
                        htmlStr += '激活时间： ' + params.data.activeTime + '<br />';
                        htmlStr += '定位方式： ';
                        if (params.data.locationWay == 1) {
                            htmlStr += '自动定位' + '<br />';
                        } else if (params.data.locationWay == 2) {
                            htmlStr += '设备定位' + '<br />';
                        } else if (params.data.locationWay == 3) {
                            htmlStr += '自定义位置' + '<br />';
                        } else {
                            htmlStr += '未知' + '<br />';
                        }
                        htmlStr += '所在地址： ' + params.data.networkAddress + '<br />';
                        htmlStr += '</div>';
                        return htmlStr;
                    },
                },
                bmap: {
                    center: [133, 38],
                    zoom: 5,
                    roam: true,
                    mapStyle: {
                        styleJson: [
                            {
                                featureType: 'water',
                                elementType: 'all',
                                stylers: {
                                    color: '#a0cfff',
                                },
                            },
                            {
                                featureType: 'land',
                                elementType: 'all',
                                stylers: {
                                    color: '#fafafa', // #fffff8 淡黄色
                                },
                            },
                            {
                                featureType: 'railway',
                                elementType: 'all',
                                stylers: {
                                    visibility: 'off',
                                },
                            },
                            {
                                featureType: 'highway',
                                elementType: 'all',
                                stylers: {
                                    color: '#fdfdfd',
                                },
                            },
                            {
                                featureType: 'highway',
                                elementType: 'labels',
                                stylers: {
                                    visibility: 'off',
                                },
                            },
                            {
                                featureType: 'arterial',
                                elementType: 'geometry',
                                stylers: {
                                    color: '#fefefe',
                                },
                            },
                            {
                                featureType: 'arterial',
                                elementType: 'geometry.fill',
                                stylers: {
                                    color: '#fefefe',
                                },
                            },
                            {
                                featureType: 'poi',
                                elementType: 'all',
                                stylers: {
                                    visibility: 'off',
                                },
                            },
                            {
                                featureType: 'green',
                                elementType: 'all',
                                stylers: {
                                    visibility: 'off',
                                },
                            },
                            {
                                featureType: 'subway',
                                elementType: 'all',
                                stylers: {
                                    visibility: 'off',
                                },
                            },
                            {
                                featureType: 'manmade',
                                elementType: 'all',
                                stylers: {
                                    color: '#d1d1d1',
                                },
                            },
                            {
                                featureType: 'local',
                                elementType: 'all',
                                stylers: {
                                    color: '#d1d1d1',
                                },
                            },
                            {
                                featureType: 'arterial',
                                elementType: 'labels',
                                stylers: {
                                    visibility: 'off',
                                },
                            },
                            {
                                featureType: 'boundary',
                                elementType: 'all',
                                stylers: {
                                    color: '#999999',
                                },
                            },
                            {
                                featureType: 'building',
                                elementType: 'all',
                                stylers: {
                                    color: '#d1d1d1',
                                },
                            },
                            {
                                featureType: 'label',
                                elementType: 'labels.text.fill',
                                stylers: {
                                    color: '#999999',
                                },
                            },
                        ],
                    },
                },
                series: [
                    {
                        type: 'scatter',
                        coordinateSystem: 'bmap',
                        data: convertData(this.deviceList, 1),
                        symbolSize: 15,
                        itemStyle: {
                            color: '#E6A23C',
                        },
                    },
                    {
                        type: 'scatter',
                        coordinateSystem: 'bmap',
                        data: convertData(this.deviceList, 2),
                        symbolSize: 15,
                        itemStyle: {
                            color: '#F56C6C',
                        },
                    },
                    {
                        type: 'scatter',
                        coordinateSystem: 'bmap',
                        data: convertData(this.deviceList, 4),
                        symbolSize: 15,
                        itemStyle: {
                            color: '#909399',
                        },
                    },
                    {
                        type: 'effectScatter',
                        coordinateSystem: 'bmap',
                        data: convertData(this.deviceList, 3),
                        symbolSize: 15,
                        showEffectOn: 'render',
                        rippleEffect: {
                            brushType: 'stroke',
                            scale: 5,
                        },
                        label: {
                            formatter: '{b}',
                            position: 'right',
                            show: false,
                        },
                        itemStyle: {
                            color: '#67C23A',
                            shadowBlur: 100,
                            shadowColor: '#333',
                        },
                        zlevel: 1,
                    },
                ],
            };

            option && myChart.setOption(option);
        },

        drawPieCpu() {
            // 基于准备好的dom，初始化echarts实例
            let myChart = this.$echarts.init(this.$refs.pieCpu);
            var option;
            option = {
                title: {
                    text: 'CPU使用率',
                    left: 'left',
                    textStyle: {
                        fontSize: 16,
                    },
                },
                tooltip: {
                    trigger: 'item',
                },
                legend: {
                    orient: 'vertical',
                    left: 'right',
                },
                color: ['#E6A23C', '#F56C6C', '#DDD'],
                series: [
                    {
                        name: 'CPU使用率 %',
                        type: 'pie',
                        radius: '55%',
                        label: {
                            show: false,
                        },
                        labelLine: {
                            normal: {
                                position: 'inner',
                                show: false,
                            },
                        },
                        data: [
                            {
                                value: this.server.cpu.used,
                                name: '用户',
                            },
                            {
                                value: this.server.cpu.sys,
                                name: '系统',
                            },
                            {
                                value: this.server.cpu.free,
                                name: '空闲',
                            },
                        ],
                    },
                ],
            };
            option && myChart.setOption(option);
        },
        drawPieMemery() {
            // 基于准备好的dom，初始化echarts实例
            let myChart = this.$echarts.init(this.$refs.pieMemery);
            var option;
            option = {
                title: {
                    text: '内存使用率',
                    left: 'left',
                    textStyle: {
                        fontSize: 16,
                    },
                },
                tooltip: {
                    trigger: 'item',
                },
                legend: {
                    orient: 'vertical',
                    left: 'right',
                },
                color: ['#F56C6C', '#DDD'],
                series: [
                    {
                        name: '内存使用率 G',
                        type: 'pie',
                        radius: '55%',
                        label: {
                            show: false,
                        },
                        labelLine: {
                            normal: {
                                position: 'inner',
                                show: false,
                            },
                        },
                        data: [
                            {
                                value: this.server.mem.used,
                                name: '已用',
                            },
                            {
                                value: this.server.mem.free,
                                name: '剩余',
                            },
                        ],
                    },
                ],
            };
            option && myChart.setOption(option);
        },
        drawPieDisk() {
            // 基于准备好的dom，初始化echarts实例
            let myChart = this.$echarts.init(this.$refs.pieDisk);
            var option;
            let one = this.server.sysFiles[0].used.replace('GB', '');
            let two = this.server.sysFiles[0].free.replace('GB', '');
            option = {
                title: {
                    text: '系统盘使用率',
                    left: 'left',
                    textStyle: {
                        fontSize: 16,
                    },
                },
                tooltip: {
                    trigger: 'item',
                },
                legend: {
                    orient: 'vertical',
                    left: 'right',
                },
                color: ['#F56C6C', '#DDD'],
                series: [
                    {
                        name: '磁盘状态 G',
                        type: 'pie',
                        radius: '55%',
                        label: {
                            show: false,
                        },
                        labelLine: {
                            normal: {
                                position: 'inner',
                                show: false,
                            },
                        },
                        data: [
                            {
                                value: one,
                                name: '已用',
                            },
                            {
                                value: two,
                                name: '可用',
                            },
                        ],
                    },
                ],
            };
            option && myChart.setOption(option);
        },
        /** 初始化地图 */
        initMap() {
            try {
                console.log('初始化天地图');

                // 初始化地图对象
                this.map = new T.Map('mapContainer');

                // 根据设备点位情况设置地图中心和缩放级别
                this.setInitialMapView();

                // 添加缩放控件
                this.map.addControl(new T.Control.Zoom());

                // 添加比例尺控件
                this.map.addControl(new T.Control.Scale());

                // 添加地图类型控件
                this.map.addControl(new T.Control.MapType());

                // 添加鹰眼控件
                this.map.addControl(new T.Control.OverviewMap());

                // 如果有设备，则添加设备标记
                if (this.deviceList.length > 0) {
                    this.addDeviceMarkers();
                }

                // 添加标题
                this.addMapTitle();

                // 添加地图缩放事件监听
                this.map.addEventListener('zoomend', () => {
                    const zoom = this.map.getZoom();
                    console.log('当前缩放级别:', zoom);
                    // 缩放级别变化时，重新渲染标记
                    this.updateMarkers(zoom);
                });
            } catch (error) {
                console.error('地图初始化失败:', error);
            }
        },

        /**
         * 设置地图的初始视图（中心点和缩放级别）
         */
        setInitialMapView() {
            // 合肥市中心坐标（默认中心点）
            const hefeiCenter = new T.LngLat(117.27, 31.86);

            if (this.deviceList && this.deviceList.length > 0) {
                try {
                    // 收集所有设备的有效点位
                    const validPoints = [];

                    this.deviceList.forEach((device) => {
                        if (device.longitude && device.latitude) {
                            const lng = parseFloat(device.longitude);
                            const lat = parseFloat(device.latitude);

                            if (!isNaN(lng) && !isNaN(lat)) {
                                validPoints.push({
                                    lng: lng,
                                    lat: lat,
                                });
                            }
                        }
                    });

                    console.log(`找到 ${validPoints.length} 个有效点位`);

                    if (validPoints.length === 0) {
                        // 没有有效点位，使用默认中心点
                        this.map.centerAndZoom(hefeiCenter, 12);
                        console.log('没有有效点位，默认显示合肥市中心');
                        return;
                    }

                    // 优先尝试使用在线设备设置视图
                    const onlinePoints = validPoints.filter((point) => {
                        const device = this.deviceList.find((d) => parseFloat(d.longitude) === point.lng && parseFloat(d.latitude) === point.lat);
                        return device && device.status === 3;
                    });

                    if (onlinePoints && onlinePoints.length > 0) {
                        console.log(`找到 ${onlinePoints.length} 个在线点位，优先使用在线设备设置视图`);
                        // 计算包含所有在线点位的最佳视图
                        const viewportInfo = this.calculateOptimalViewport(onlinePoints);

                        // 设置地图视图
                        this.setMapViewFromInfo(viewportInfo, '在线设备');
                        return;
                    }

                    // 如果没有在线设备，使用所有设备点位
                    console.log('没有在线设备，使用所有设备点位设置视图');
                    const viewportInfo = this.calculateOptimalViewport(validPoints);

                    // 设置地图视图
                    this.setMapViewFromInfo(viewportInfo, '所有设备');
                } catch (error) {
                    console.error('计算地图视图失败:', error);
                    // 出错时使用默认视图
                    this.map.centerAndZoom(hefeiCenter, 12);
                    console.log('计算失败，默认显示合肥市中心');
                }
            } else {
                // 没有设备点位，使用默认中心点
                this.map.centerAndZoom(hefeiCenter, 12);
                console.log('没有设备数据，默认显示合肥市中心');
            }
        },

        /**
         * 计算最佳视口信息
         * @param {Array} points 点位数组
         * @returns {Object} 包含中心点、边界和推荐缩放级别的对象
         */
        calculateOptimalViewport(points) {
            console.log('开始计算最佳视口...');

            // 打印所有点位进行调试
            console.log('所有点位:', JSON.stringify(points));

            // 计算所有点位的边界
            let minLng = Number.MAX_VALUE;
            let maxLng = Number.MIN_VALUE;
            let minLat = Number.MAX_VALUE;
            let maxLat = Number.MIN_VALUE;

            points.forEach((point) => {
                minLng = Math.min(minLng, point.lng);
                maxLng = Math.max(maxLng, point.lng);
                minLat = Math.min(minLat, point.lat);
                maxLat = Math.max(maxLat, point.lat);
            });

            console.log('原始边界:', { minLng, maxLng, minLat, maxLat });

            // 计算边界框的中心
            const centerLng = (minLng + maxLng) / 2;
            const centerLat = (minLat + maxLat) / 2;

            // 计算边界框的宽度和高度（度数）
            const lngDelta = maxLng - minLng;
            const latDelta = maxLat - minLat;

            console.log('边界范围:', { lngDelta: lngDelta, latDelta: latDelta });

            // 如果点位太少且分布很分散或者分布在一条直线上，需要特殊处理
            // 检测是否所有点位都在同一经线或同一纬线上
            const isAllSameLongitude = lngDelta < 0.0001;
            const isAllSameLatitude = latDelta < 0.0001;

            // 为极端情况（点位在一条线上或非常集中）添加最小边界范围
            const minBoundary = 0.05; // 最小边界范围(约5公里左右)

            if (isAllSameLongitude) {
                minLng -= minBoundary;
                maxLng += minBoundary;
            }

            if (isAllSameLatitude) {
                minLat -= minBoundary;
                maxLat += minBoundary;
            }

            // 为边界添加较大缓冲区，确保点位不会刚好在视口边缘
            // 对于点位少的情况，增加更大的缓冲区
            let bufferFactor = 0.1; // 基本缓冲系数: 10%

            // 根据点位数量和分布调整缓冲区
            if (points.length <= 5) {
                // 点位很少时使用更大的缓冲区
                bufferFactor = 0.2; // 20%的缓冲区

                // 如果点位分布很分散，使用更大的缓冲区
                if (lngDelta > 1 || latDelta > 1) {
                    bufferFactor = 0.3; // 30%的缓冲区
                }
            }

            // 确保边界有最小宽度和高度
            const minLngSpan = 0.05; // 约5公里
            const minLatSpan = 0.05;

            if (lngDelta < minLngSpan) {
                const expandBy = (minLngSpan - lngDelta) / 2;
                minLng -= expandBy;
                maxLng += expandBy;
            }

            if (latDelta < minLatSpan) {
                const expandBy = (minLatSpan - latDelta) / 2;
                minLat -= expandBy;
                maxLat += expandBy;
            }

            // 添加缓冲区
            const bufferedLngDelta = maxLng - minLng;
            const bufferedLatDelta = maxLat - minLat;

            const bufferedMinLng = minLng - bufferedLngDelta * bufferFactor;
            const bufferedMaxLng = maxLng + bufferedLngDelta * bufferFactor;
            const bufferedMinLat = minLat - bufferedLatDelta * bufferFactor;
            const bufferedMaxLat = maxLat + bufferedLatDelta * bufferFactor;

            console.log('带缓冲区的边界:', {
                minLng: bufferedMinLng,
                maxLng: bufferedMaxLng,
                minLat: bufferedMinLat,
                maxLat: bufferedMaxLat,
            });

            // 推算合适的缩放级别
            let zoomLevel = this.calculateZoomLevel(bufferedMinLng, bufferedMaxLng, bufferedMinLat, bufferedMaxLat);

            console.log('初步计算的缩放级别:', zoomLevel);

            // 点位少时特殊调整
            if (points.length === 1) {
                zoomLevel = 13; // 单点使用更合适的缩放级别
            } else if (points.length <= 5) {
                // 对于少量点，强制缩小视图以确保能看到所有点
                zoomLevel = Math.min(zoomLevel, 10);

                // 如果点位分布很分散，缩放级别需要更小
                if (lngDelta > 0.5 || latDelta > 0.5) {
                    zoomLevel = Math.min(zoomLevel, 8);
                }

                // 如果点位分布极度分散，进一步减小缩放级别
                if (lngDelta > 2 || latDelta > 2) {
                    zoomLevel = Math.min(zoomLevel, 6);
                }
            }

            // 确保缩放级别在有效范围内，偏向于缩小视图以便能看到所有点位
            zoomLevel = Math.max(4, Math.min(16, zoomLevel));

            // 如果点位少且计算的缩放级别过大，强制降低缩放级别
            if (points.length <= 10 && zoomLevel > 10) {
                zoomLevel = 10;
            }

            console.log('最终计算的视图信息:', {
                center: { lng: centerLng, lat: centerLat },
                bounds: {
                    original: { minLng, maxLng, minLat, maxLat },
                    buffered: { minLng: bufferedMinLng, maxLng: bufferedMaxLng, minLat: bufferedMinLat, maxLat: bufferedMaxLat },
                },
                zoomLevel: zoomLevel,
                pointsCount: points.length,
            });

            return {
                center: { lng: centerLng, lat: centerLat },
                bounds: {
                    minLng: bufferedMinLng,
                    maxLng: bufferedMaxLng,
                    minLat: bufferedMinLat,
                    maxLat: bufferedMaxLat,
                },
                zoomLevel: zoomLevel,
            };
        },

        /**
         * 根据地理范围计算合适的缩放级别
         * @param {number} minLng 最小经度
         * @param {number} maxLng 最大经度
         * @param {number} minLat 最小纬度
         * @param {number} maxLat 最大纬度
         * @returns {number} 推荐的缩放级别
         */
        calculateZoomLevel(minLng, maxLng, minLat, maxLat) {
            console.log('计算缩放级别, 范围:', { minLng, maxLng, minLat, maxLat });

            // 获取地图容器尺寸
            const mapContainer = document.getElementById('mapContainer');
            if (!mapContainer) {
                console.warn('未找到地图容器，使用默认缩放级别');
                return 8; // 默认较小的缩放级别以显示更大范围
            }

            const mapWidth = mapContainer.clientWidth;
            const mapHeight = mapContainer.clientHeight;

            console.log('地图容器尺寸:', { width: mapWidth, height: mapHeight });

            // 计算经纬度范围
            const lngSpan = maxLng - minLng;
            const latSpan = maxLat - minLat;

            if (lngSpan <= 0 || latSpan <= 0) {
                console.warn('无效的经纬度范围，使用默认缩放级别');
                return 8;
            }

            // 根据经度跨度计算缩放级别
            // 调整公式，降低计算出的缩放级别，确保能显示更大范围
            const zoomLng = Math.log2(360 / lngSpan) + Math.log2(mapWidth / 256) - 0.5;

            // 根据纬度跨度计算缩放级别
            const latRadian = (latSpan * Math.PI) / 180;
            // 同样为纬度缩放级别减小一些，确保显示更大范围
            const zoomLat = Math.log2((2 * Math.PI) / latRadian) + Math.log2(mapHeight / 256) - 0.5;

            console.log('计算的缩放级别:', { zoomLng, zoomLat });

            // 取较小的缩放级别，确保所有点位都在视口内
            let zoom = Math.min(zoomLng, zoomLat);

            // 向下取整，并确保缩放级别不会过大
            zoom = Math.floor(zoom);

            // 如果计算结果异常，使用保守的缩放级别
            if (isNaN(zoom) || zoom < 0 || zoom > 18) {
                console.warn('计算出的缩放级别异常，使用默认值');
                zoom = 8;
            }

            return zoom;
        },

        /**
         * 根据视口信息设置地图视图
         * @param {Object} viewportInfo 视口信息
         * @param {string} sourceDesc 数据来源描述
         */
        setMapViewFromInfo(viewportInfo, sourceDesc) {
            if (!viewportInfo) {
                console.error('视口信息无效');
                return;
            }

            // 添加验证，确保视口信息不会导致地图显示在没有点位的区域
            if (!this.isViewportValid(viewportInfo)) {
                console.warn('视口信息异常，使用更安全的视图设置');

                // 创建一个中国范围的视图作为备选
                const chinaViewport = {
                    center: { lng: 105, lat: 35 },
                    zoomLevel: 4, // 显示整个中国的缩放级别
                };

                // 使用中国视图
                const mapCenter = new T.LngLat(chinaViewport.center.lng, chinaViewport.center.lat);
                this.map.centerAndZoom(mapCenter, chinaViewport.zoomLevel);

                console.log('已切换到中国范围视图');
                return;
            }

            const { center, zoomLevel } = viewportInfo;
            const mapCenter = new T.LngLat(center.lng, center.lat);

            // 设置地图中心点和缩放级别
            this.map.centerAndZoom(mapCenter, zoomLevel);

            console.log(`已根据${sourceDesc}点位设置地图中心和缩放级别`, {
                center: center,
                zoom: zoomLevel,
            });

            // 添加一个延时检查，验证是否成功显示了点位
            setTimeout(() => {
                this.verifyPointsInView();
            }, 1000);
        },

        /**
         * 验证视口信息是否有效
         * @param {Object} viewportInfo 视口信息
         * @returns {boolean} 是否有效
         */
        isViewportValid(viewportInfo) {
            if (!viewportInfo || !viewportInfo.center || typeof viewportInfo.zoomLevel !== 'number') {
                return false;
            }

            const { center, zoomLevel } = viewportInfo;

            // 检查经纬度是否在合理范围内
            // 中国大致范围：经度73-135，纬度3-53
            if (center.lng < 70 || center.lng > 140 || center.lat < 0 || center.lat > 60) {
                console.warn('计算的中心点超出中国范围');
                return false;
            }

            // 检查缩放级别是否合理
            if (zoomLevel < 2 || zoomLevel > 18) {
                console.warn('计算的缩放级别异常');
                return false;
            }

            return true;
        },

        /**
         * 验证当前视图中是否包含点位
         */
        verifyPointsInView() {
            // 获取当前地图显示的范围
            const bounds = this.map.getBounds();
            if (!bounds) {
                console.warn('无法获取当前地图范围');
                return;
            }

            const sw = bounds.getSouthWest();
            const ne = bounds.getNorthEast();

            const minLng = sw.lng;
            const maxLng = ne.lng;
            const minLat = sw.lat;
            const maxLat = ne.lat;

            console.log('当前地图视图范围:', { minLng, maxLng, minLat, maxLat });

            // 检查是否有点位在当前视图内
            let pointsInView = 0;

            this.deviceList.forEach((device) => {
                if (device.longitude && device.latitude) {
                    const lng = parseFloat(device.longitude);
                    const lat = parseFloat(device.latitude);

                    if (!isNaN(lng) && !isNaN(lat)) {
                        if (lng >= minLng && lng <= maxLng && lat >= minLat && lat <= maxLat) {
                            pointsInView++;
                        }
                    }
                }
            });

            console.log(`当前视图中有 ${pointsInView} 个点位, 总点位数: ${this.deviceList.length}`);

            // 如果没有点位在视图中，尝试调整视图
            if (pointsInView === 0 && this.deviceList.length > 0) {
                console.warn('当前视图中没有点位，尝试调整视图');

                // 收集所有有效点位
                const validPoints = [];
                this.deviceList.forEach((device) => {
                    if (device.longitude && device.latitude) {
                        const lng = parseFloat(device.longitude);
                        const lat = parseFloat(device.latitude);

                        if (!isNaN(lng) && !isNaN(lat)) {
                            validPoints.push({ lng, lat });
                        }
                    }
                });

                if (validPoints.length > 0) {
                    // 使用更保守的方法重新设置视图
                    const center = this.calculateCenterPoint(validPoints);

                    // 使用较小的缩放级别以显示更大范围
                    const emergencyZoomLevel = 5;

                    this.map.centerAndZoom(new T.LngLat(center.lng, center.lat), emergencyZoomLevel);
                    console.log('已切换到应急视图，缩放级别:', emergencyZoomLevel);
                }
            }
        },

        /**
         * 找出点位中密度最高的区域
         * @param {Array} points 点位数组，每个点包含lng和lat属性
         * @returns {Object} 包含中心点和边界的对象
         */
        findDensestCluster(points) {
            if (!points || points.length === 0) {
                return null;
            }

            // 如果只有少量点位，直接计算包含所有点的最佳视图
            if (points.length <= 5) {
                return this.calculateOptimalViewport(points);
            }

            // 计算所有点的中心
            const allPointsCenter = this.calculateCenterPoint(points);

            // 将点位按照到中心点的距离排序
            const sortedPoints = [...points].sort((a, b) => {
                const distA = this.calculateDistance(a.lng, a.lat, allPointsCenter.lng, allPointsCenter.lat);
                const distB = this.calculateDistance(b.lng, b.lat, allPointsCenter.lng, allPointsCenter.lat);
                return distA - distB;
            });

            // 取距离中心最近的80%的点位
            const includedPoints = sortedPoints.slice(0, Math.ceil(sortedPoints.length * 0.8));

            // 计算这些点的边界
            let minLng = Number.MAX_VALUE;
            let maxLng = Number.MIN_VALUE;
            let minLat = Number.MAX_VALUE;
            let maxLat = Number.MIN_VALUE;

            includedPoints.forEach((point) => {
                minLng = Math.min(minLng, point.lng);
                maxLng = Math.max(maxLng, point.lng);
                minLat = Math.min(minLat, point.lat);
                maxLat = Math.max(maxLat, point.lat);
            });

            // 为边界添加缓冲区
            const lngDelta = maxLng - minLng;
            const latDelta = maxLat - minLat;
            const bufferFactor = 0.1;
            minLng -= lngDelta * bufferFactor;
            maxLng += lngDelta * bufferFactor;
            minLat -= latDelta * bufferFactor;
            maxLat += latDelta * bufferFactor;

            // 计算密集点的中心
            const center = this.calculateCenterPoint(includedPoints);

            return {
                center: center,
                bounds: {
                    minLng,
                    maxLng,
                    minLat,
                    maxLat,
                },
            };
        },

        /**
         * 计算点集的中心点
         * @param {Array} points 点位数组
         * @returns {Object} 中心点坐标
         */
        calculateCenterPoint(points) {
            // 采用加权计算方式，更倾向于点位密集区域
            // 首先计算简单中心
            let sumLng = 0;
            let sumLat = 0;

            points.forEach((point) => {
                sumLng += point.lng;
                sumLat += point.lat;
            });

            return {
                lng: sumLng / points.length,
                lat: sumLat / points.length,
            };
        },

        /** 添加设备标记 */
        addDeviceMarkers() {
            try {
                console.log('添加设备标记');

                // 检查设备列表是否有效
                if (!this.deviceList || !Array.isArray(this.deviceList)) {
                    console.error('设备列表无效');
                    return;
                }

                // 记录当前缩放级别
                const zoom = this.map.getZoom();

                // 根据缩放级别添加合适的标记
                this.updateMarkers(zoom);
            } catch (error) {
                console.error('添加设备标记失败:', error);
            }
        },

        /**
         * 根据缩放级别更新标记
         * @param {number} zoom 缩放级别
         */
        updateMarkers(zoom) {
            try {
                // 清除所有现有标记
                this.map.clearOverLays();

                // 在高缩放级别（大于等于12）时显示所有点位
                // 在低缩放级别（小于12）时使用聚合
                if (zoom >= 12) {
                    this.addAllMarkers();
                } else {
                    this.addClusteredMarkers();
                }

                // 重新添加标题（因为清除了所有覆盖物）
                this.addMapTitle();
            } catch (error) {
                console.error('更新标记失败:', error);
            }
        },

        /**
         * 创建设备状态的SVG图标
         * @param {Object} device 设备信息
         * @returns {string} SVG图标的Data URL
         */
        createDeviceSvgIcon(device) {
            try {
                console.log("device.status:" + device.status)
                // 判断设备是否在线
                const isOnline = device.status === 3;

                // 设置颜色
                const fillColor = isOnline ? '#4CD964' : '#8E8E93'; // 在线绿色，离线灰色
                const pulseColor = isOnline ? 'rgba(76, 217, 100, 0.8)' : 'rgba(142, 142, 147, 0.8)'; // 对应的脉动色

                // 创建SVG
                let svg = `
                <svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" viewBox="0 0 32 32">
                    <!-- 脉动效果-外环 -->
                    <circle cx="16" cy="16" r="14" fill="none" stroke="${pulseColor}0.1)" stroke-width="${isOnline ? 1.5 : 1}">
                        <animate attributeName="r" values="9;14" dur="1.5s" repeatCount="indefinite" />
                        <animate attributeName="stroke-opacity" values="1;0" dur="1.5s" repeatCount="indefinite" />
                    </circle>

                    <!-- 脉动效果-中环 -->
                    <circle cx="16" cy="16" r="7" fill="none" stroke="${pulseColor}0.3)" stroke-width="${isOnline ? 2 : 1.2}">
                        <animate attributeName="r" values="6;11" dur="1.5s" repeatCount="indefinite" />
                        <animate attributeName="stroke-opacity" values="0.8;0.1" dur="1.5s" repeatCount="indefinite" />
                    </circle>

                    <!-- 设备点主体 -->
                    <circle cx="16" cy="16" r="6" fill="${fillColor}" stroke="${isOnline ? '#4CD964' : '#E5E5EA'}" stroke-width="1.5">
                        ${isOnline ? '<animate attributeName="r" values="5;6;5" dur="2s" repeatCount="indefinite" />' : ''}
                    </circle>

                    <!-- 设备点内部 -->
                    <circle cx="16" cy="16" r="3" fill="${isOnline ? '#fff' : '#D1D1D6'}" opacity="${isOnline ? '0.9' : '0.7'}" />
                </svg>`;

                // 转换为Data URL
                return 'data:image/svg+xml;charset=utf-8,' + encodeURIComponent(svg);
            } catch (error) {
                console.error('创建设备SVG图标失败:', error);
                // 返回默认图标
                return "data:image/svg+xml;charset=utf-8,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 16 16'%3E%3Ccircle cx='8' cy='8' r='6' fill='%23999'/%3E%3C/svg%3E";
            }
        },

        /**
         * 创建设备标记
         * @param {Object} device 设备信息
         * @param {T.LngLat} lnglat 经纬度对象
         * @returns {T.Marker} 标记对象
         */
        createDeviceMarker(device, lnglat) {
            try {
                // 获取设备SVG图标的Data URL
                const iconUrl = this.createDeviceSvgIcon(device);

                // 创建图标对象
                const icon = new T.Icon({
                    iconUrl: iconUrl,
                    iconSize: new T.Point(32, 32),
                    iconAnchor: new T.Point(16, 16),
                });

                // 创建标记
                const marker = new T.Marker(lnglat, { icon: icon });

                // 添加点击事件
                marker.addEventListener('click', () => {
                    console.log('设备标记被点击:', device);
                    // 显示设备信息窗口
                    this.showDeviceInfoWindow(device, lnglat);
                });

                return marker;
            } catch (error) {
                console.error('创建设备标记失败:', error);
                return null;
            }
        },

        /**
         * 添加所有标记点
         */
        addAllMarkers() {
            try {
                console.log('添加所有标记点');
                let validMarkers = 0;

                this.deviceList.forEach((device) => {
                    if (!device.longitude || !device.latitude) {
                        return;
                    }

                    const marker = this.createDeviceMarker(device, new T.LngLat(device.longitude, device.latitude));

                    this.map.addOverLay(marker);
                    validMarkers++;
                });

                console.log(`成功添加 ${validMarkers} 个标记点`);
            } catch (error) {
                console.error('添加所有标记失败:', error);
            }
        },

        /**
         * 添加聚合标记
         */
        addClusteredMarkers() {
            try {
                console.log('添加聚合标记');
                // 根据当前缩放级别动态调整聚合距离阈值（千米）
                const zoom = this.map.getZoom();
                const distanceThreshold = 2 * Math.pow(1.5, 8 - zoom); // 随着缩放级别增加而减小，单位：千米

                // 第一步：按行政区域分组
                // 如果设备数据中没有行政区域信息，则根据经纬度大致划分
                const regionClusters = {};

                this.deviceList.forEach((device) => {
                    if (!device.longitude || !device.latitude) {
                        return;
                    }

                    const lng = parseFloat(device.longitude);
                    const lat = parseFloat(device.latitude);

                    if (isNaN(lng) || isNaN(lat)) {
                        return;
                    }

                    // 优先使用设备自带的行政区信息
                    let regionKey = device.regionName || device.areaName || device.district;

                    // 如果没有行政区信息，则根据经纬度划分区域
                    // 将经纬度按照0.5度网格划分
                    if (!regionKey) {
                        const lngGrid = Math.floor(lng * 2) / 2;
                        const latGrid = Math.floor(lat * 2) / 2;
                        regionKey = `grid_${lngGrid}_${latGrid}`;
                    }

                    // 初始化该区域的聚合集合
                    if (!regionClusters[regionKey]) {
                        regionClusters[regionKey] = [];
                    }

                    // 将设备添加到对应的区域
                    regionClusters[regionKey].push({
                        lng: lng,
                        lat: lat,
                        device: device,
                    });
                });

                // 第二步：在每个行政区内按距离聚合
                const finalClusters = [];

                Object.keys(regionClusters).forEach((regionKey) => {
                    const regionDevices = regionClusters[regionKey];

                    // 区域内距离聚合
                    const regionDistanceClusters = [];

                    regionDevices.forEach((devicePoint) => {
                        // 检查是否可以加入现有聚合
                        let addedToCluster = false;

                        for (const cluster of regionDistanceClusters) {
                            const centerPoint = cluster.center;
                            const distance = this.calculateDistance(centerPoint.lng, centerPoint.lat, devicePoint.lng, devicePoint.lat);

                            if (distance < distanceThreshold) {
                                // 加入现有聚合
                                cluster.points.push(devicePoint);
                                // 重新计算中心点（简单平均）
                                const totalPoints = cluster.points.length;
                                cluster.center = {
                                    lng: (cluster.center.lng * (totalPoints - 1) + devicePoint.lng) / totalPoints,
                                    lat: (cluster.center.lat * (totalPoints - 1) + devicePoint.lat) / totalPoints,
                                };
                                addedToCluster = true;
                                break;
                            }
                        }

                        // 如果没有加入任何聚合，则创建新的聚合
                        if (!addedToCluster) {
                            regionDistanceClusters.push({
                                center: {
                                    lng: devicePoint.lng,
                                    lat: devicePoint.lat,
                                },
                                points: [devicePoint],
                                regionKey: regionKey, // 保存区域信息
                            });
                        }
                    });

                    // 将该区域的聚合添加到最终聚合列表
                    finalClusters.push(...regionDistanceClusters);
                });

                console.log(`创建了 ${finalClusters.length} 个聚合点，来自 ${Object.keys(regionClusters).length} 个区域`);

                // 添加聚合点或单个点到地图
                finalClusters.forEach((cluster, index) => {
                    const lnglat = new T.LngLat(cluster.center.lng, cluster.center.lat);
                    console.log(`处理第 ${index + 1} 个聚合点(${cluster.regionKey})，位置:`, lnglat, `包含 ${cluster.points.length} 个点`);

                    if (cluster.points.length > 1) {
                        try {
                            // 创建一个临时的canvas来生成聚合点图标
                            const canvas = document.createElement('canvas');
                            const size = 44; // 保持尺寸
                            canvas.width = size;
                            canvas.height = size;
                            const ctx = canvas.getContext('2d');

                            // 添加阴影效果
                            ctx.shadowColor = 'rgba(0, 0, 0, 0.3)';
                            ctx.shadowBlur = 5;
                            ctx.shadowOffsetX = 0;
                            ctx.shadowOffsetY = 2;

                            // 根据区域生成不同的颜色
                            // 使用简单的哈希算法将区域键转为颜色
                            const regionHash = this.hashString(cluster.regionKey);
                            const hue = regionHash % 360; // 0-360色相
                            const fillColor = `hsla(${hue}, 80%, 50%, 0.8)`;

                            // 绘制聚合点背景
                            ctx.beginPath();
                            ctx.arc(size / 2, size / 2, 20, 0, 2 * Math.PI); // 小圆点半径15px
                            ctx.fillStyle = fillColor;
                            ctx.fill();

                            // 内圈高光效果（可选）
                            ctx.beginPath();
                            ctx.arc(size / 2, size / 2 - 3, 8, 0, 2 * Math.PI);
                            const gradient = ctx.createRadialGradient(size / 2, size / 2 - 3, 0, size / 2, size / 2 - 3, 8);
                            gradient.addColorStop(0, 'rgba(255, 255, 255, 0.3)');
                            gradient.addColorStop(1, `hsla(${hue}, 80%, 50%, 0)`);
                            ctx.fillStyle = gradient;
                            ctx.fill();

                            // 添加聚合数字
                            ctx.shadowColor = 'rgba(0, 0, 0, 0)'; // 移除文本阴影
                            ctx.fillStyle = '#FFFFFF';
                            ctx.font = 'bold 14px -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif';
                            ctx.textAlign = 'center';
                            ctx.textBaseline = 'middle';
                            ctx.fillText(cluster.points.length.toString(), size / 2, size / 2);

                            // 创建图标
                            const icon = new T.Icon({
                                iconUrl: canvas.toDataURL(),
                                iconSize: new T.Point(size, size),
                                iconAnchor: new T.Point(size / 2, size / 2),
                            });

                            // 创建标记
                            const marker = new T.Marker(lnglat, { icon: icon });

                            // 添加点击事件
                            marker.addEventListener('click', () => {
                                // 直接切换到显示所有点的级别
                                this.map.setZoom(12);
                                this.map.panTo(lnglat);

                                // 显示该聚合内的所有点
                                cluster.points.forEach((point) => {
                                    const deviceMarker = this.createDeviceMarker(point.device, new T.LngLat(point.lng, point.lat));
                                    this.map.addOverLay(deviceMarker);
                                });
                            });

                            // 添加到地图
                            this.map.addOverLay(marker);
                            console.log(`成功添加聚合标记 ${index + 1}`);
                        } catch (err) {
                            console.error(`创建聚合标记 ${index + 1} 失败:`, err);
                        }
                    } else if (cluster.points.length === 1) {
                        try {
                            const device = cluster.points[0].device;
                            // 创建单个设备标记
                            const marker = this.createDeviceMarker(device, lnglat);
                            if (marker) {
                                this.map.addOverLay(marker);
                                console.log(`成功添加单个标记 ${index + 1}`);
                            }
                        } catch (err) {
                            console.error(`创建单个标记 ${index + 1} 失败:`, err);
                        }
                    }
                });
            } catch (error) {
                console.error('添加聚合标记失败:', error);
            }
        },

        /**
         * 简单的字符串哈希函数，用于生成区域颜色
         * @param {string} str 输入字符串
         * @returns {number} 哈希值
         */
        hashString(str) {
            let hash = 0;
            if (!str || str.length === 0) return hash;

            for (let i = 0; i < str.length; i++) {
                const char = str.charCodeAt(i);
                hash = (hash << 5) - hash + char;
                hash = hash & hash; // Convert to 32bit integer
            }

            // 确保哈希值为正数
            return Math.abs(hash);
        },

        /**
         * 计算两点之间的距离（单位：千米）
         * 使用Haversine公式计算地球表面两点间的大圆距离
         * @param {number} lng1 第一个点的经度
         * @param {number} lat1 第一个点的纬度
         * @param {number} lng2 第二个点的经度
         * @param {number} lat2 第二个点的纬度
         * @returns {number} 两点之间的距离（千米）
         */
        calculateDistance(lng1, lat1, lng2, lat2) {
            const radLat1 = (lat1 * Math.PI) / 180.0;
            const radLat2 = (lat2 * Math.PI) / 180.0;
            const a = radLat1 - radLat2;
            const b = (lng1 * Math.PI) / 180.0 - (lng2 * Math.PI) / 180.0;
            let s = 2 * Math.asin(Math.sqrt(Math.pow(Math.sin(a / 2), 2) + Math.cos(radLat1) * Math.cos(radLat2) * Math.pow(Math.sin(b / 2), 2)));
            // 地球平均半径，6371.0千米
            s = s * 6371.0;
            return s;
        },

        /**
         * 添加地图标题
         */
        addMapTitle() {
            try {
                // 创建标题内容
                const titleDiv = document.createElement('div');
                titleDiv.style.cssText = 'position:absolute;left:50%;top:10px;transform:translateX(-50%);background:white;padding:5px;border-radius:3px;z-index:1000;font-weight:bold;font-size:18px;';
                titleDiv.innerHTML = `设备分布（设备总数${this.deviceList.length}   在线数 ${this.deviceList.filter((x) => x.status === 3).length}）`;

                // 获取地图容器
                const mapContainer = document.getElementById('mapContainer');
                if (mapContainer) {
                    mapContainer.appendChild(titleDiv);
                } else {
                    console.error('未找到地图容器');
                }
            } catch (error) {
                console.error('添加标题控件失败:', error);
            }
        },

        /**
         * 显示设备信息窗口
         * @param {Object} device 设备信息
         * @param {T.LngLat} lnglat 经纬度对象
         */
        showDeviceInfoWindow(device, lnglat) {
            try {
                // 创建信息窗口内容
                const content = this.createDeviceInfoContent(device);

                // 创建信息窗口
                const infoWindow = new T.InfoWindow(content, {
                    offset: new T.Point(0, -16),
                    closeButton: true,
                    autoPan: true,
                    width: 280, // 设置宽度
                    minWidth: 240,
                    maxWidth: 360,
                });

                // 打开信息窗口
                this.map.openInfoWindow(infoWindow, lnglat);
            } catch (error) {
                console.error('显示设备信息窗口失败:', error);
            }
        },

        /**
         * 创建设备信息内容
         * @param {Object} device 设备信息
         * @returns {String} 信息窗口HTML内容
         */
        createDeviceInfoContent(device) {
            // 判断设备是否在线
            const isOnline = device.status === 3;
            const statusClass = isOnline ? 'online-status' : 'offline-status';
            const statusText = isOnline ? '在线' : '离线';
            device.statusName = statusText;

            // 格式化上次上线时间
            let lastOnlineTime = device.lastOnlineTime || '未知';
            if (lastOnlineTime && lastOnlineTime !== '未知') {
                // 假设时间格式为ISO格式或时间戳
                try {
                    const date = new Date(lastOnlineTime);
                    if (!isNaN(date.getTime())) {
                        lastOnlineTime = date.toLocaleString();
                    }
                } catch (e) {
                    console.warn('时间格式化失败:', e);
                }
            }

            return `
            <div class="device-info-window">
                <div class="device-header">
                    <h3 class="device-name">${device.deviceName || '未命名设备'}</h3>
                    <div class="device-status ${statusClass}">${statusText}</div>
                </div>
                <div class="device-detail">
                    <p><strong>设备编号:</strong> ${device.serialNumber || '未知'}</p>
                    <p><strong>设备名称:</strong> ${device.deviceName || '未知'}</p>
                    <p><strong>产品名称:</strong> ${device.productName || '未知'}</p>
                    <p><strong>联网地址:</strong> ${device.networkAddress || '未知'}</p>
                    <p><strong>经度:</strong> ${device.longitude || '未知'}</p>
                    <p><strong>纬度:</strong> ${device.latitude || '未知'}</p>
                    <p><strong>运行状态:</strong> ${device.statusName || '未知'}</p>
                    <p><strong>更新时间:</strong> ${device.activeTime || '未知'}</p>
                </div>
                <div class="device-footer">
                    <button class="detail-btn" onclick="window.viewDeviceDetail('${device.id || device.deviceId}')">查看详情</button>
                </div>
                <style>
                    .device-info-window {
                        font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
                        padding: 5px;
                    }
                    .device-header {
                        display: flex;
                        justify-content: space-between;
                        align-items: center;
                        border-bottom: 1px solid #eee;
                        padding-bottom: 8px;
                        margin-bottom: 8px;
                    }
                    .device-name {
                        margin: 0;
                        font-size: 16px;
                        color: #333;
                        white-space: nowrap;
                        overflow: hidden;
                        text-overflow: ellipsis;
                        max-width: 180px;
                    }
                    .device-status {
                        padding: 3px 8px;
                        border-radius: 12px;
                        font-size: 12px;
                        font-weight: bold;
                    }
                    .online-status {
                        background-color: rgba(76, 217, 100, 0.15);
                        color: #4CD964;
                    }
                    .offline-status {
                        background-color: rgba(142, 142, 147, 0.15);
                        color: #8E8E93;
                    }
                    .device-detail {
                        font-size: 13px;
                        line-height: 1.5;
                    }
                    .device-detail p {
                        margin: 4px 0;
                    }
                    .device-footer {
                        margin-top: 12px;
                        display: flex;
                        justify-content: flex-end;
                    }
                    .detail-btn {
                        background-color: #007AFF;
                        color: white;
                        border: none;
                        padding: 5px 12px;
                        border-radius: 4px;
                        cursor: pointer;
                        font-size: 12px;
                    }
                    .detail-btn:hover {
                        background-color: #0062CC;
                    }
                </style>
            </div>
            `;
        },

        /**
         * 跳转到设备详情页面
         */
        viewDeviceDetail(deviceId) {
            this.$router.push({
                path: '/iot/device-edit',
                query: {
                    t: Date.now(),
                    deviceId: deviceId,
                },
            });
        },
    },
};
</script>

<style lang="scss" scoped>
.phone {
    height: 729px;
    width: 370px;

    background-size: cover;
    margin: 0 auto;
}

.phone-container {
    height: 618px;
    width: 343px;
    position: relative;
    top: 46px;
    left: 12px;
    background-color: #fff;
}

.content {
    line-height: 24px;
    padding: 10px;
    border: 1px solid #eee;
    border-radius: 10px;
}

.description {
    font-size: 12px;

    tr {
        line-height: 20px;
    }
}

.panel-group {
    .card-panel-col {
        margin-bottom: 10px;
    }

    .card-panel {
        height: 68px;
        cursor: pointer;
        position: relative;
        overflow: hidden;
        color: #666;
        border: 1px solid #eee;
        border-radius: 5px;
        //box-shadow: 0px 1px 2px rgba(0, 0, 0, 0.08);
        background-color: #fff;

        &:hover {
            .card-panel-icon-wrapper {
                color: #fff;
            }

            .icon-blue {
                background: #36a3f7;
            }

            .icon-green {
                background: #34bfa3;
            }

            .icon-red {
                background: #f56c6c;
            }

            .icon-orange {
                background: #e6a23c;
            }
        }

        .icon-blue {
            color: #36a3f7;
        }

        .icon-green {
            color: #34bfa3;
        }

        .icon-red {
            color: #f56c6c;
        }

        .icon-orange {
            color: #e6a23c;
        }

        .card-panel-icon-wrapper {
            float: left;
            margin: 10px;
            padding: 10px;
            transition: all 0.38s ease-out;
            border-radius: 6px;
        }

        .card-panel-icon {
            float: left;
            font-size: 30px;
        }

        .card-panel-description {
            float: right;
            font-weight: bold;
            margin: 15px;
            margin-left: 0px;

            .card-panel-text {
                line-height: 14px;
                color: rgba(0, 0, 0, 0.45);
                font-size: 14px;
                margin-bottom: 12px;
                text-align: right;
            }

            .card-panel-num {
                font-size: 18px;
            }
        }
    }
}
</style>
