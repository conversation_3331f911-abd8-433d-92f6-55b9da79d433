<template>
    <div class="app-container">
        <el-card class="top-card-wrap">
            <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
                <el-form-item label="场景类型" prop="typeName">
                    <!-- <el-input v-model="queryParams.typeName" placeholder="请输入场景类型" clearable
                        @keyup.enter.native="handleQuery" /> -->
                    <el-select v-model="queryParams.typeName" filterable placeholder="请选择场景类型">
                        <el-option v-for="item in typeNamedata" :key="item.typeId" :label="item.typeName" :value="item.typeName"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
                    <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
                </el-form-item>
            </el-form>
            <el-row :gutter="10" class="mb8">
                <el-col :span="1.5">
                    <el-button type="info" plain icon="el-icon-sort" size="mini" @click="toggleExpandAll">展开/折叠</el-button>
                </el-col>
                <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
            </el-row>
            <!--      <el-row :gutter="10" class="mb8">-->
            <!--        <el-col :span="1.5">-->
            <!--          <el-button-->
            <!--            type="primary"-->
            <!--            plain-->
            <!--            icon="el-icon-plus"-->
            <!--            size="mini"-->
            <!--            @click="handleAdd"-->
            <!--            v-hasPermi="['iot:scenarioType:add']"-->
            <!--          >新增</el-button>-->
            <!--        </el-col>-->
            <!--        <el-col :span="1.5">-->
            <!--          <el-button-->
            <!--            type="success"-->
            <!--            plain-->
            <!--            icon="el-icon-edit"-->
            <!--            size="mini"-->
            <!--            :disabled="single"-->
            <!--            @click="handleUpdate"-->
            <!--            v-hasPermi="['iot:scenarioType:edit']"-->
            <!--          >修改</el-button>-->
            <!--        </el-col>-->
            <!--        <el-col :span="1.5">-->
            <!--          <el-button-->
            <!--            type="danger"-->
            <!--            plain-->
            <!--            icon="el-icon-delete"-->
            <!--            size="mini"-->
            <!--            :disabled="multiple"-->
            <!--            @click="handleDelete"-->
            <!--            v-hasPermi="['iot:scenarioType:remove']"-->
            <!--          >删除</el-button>-->
            <!--        </el-col>-->
            <!--        <el-col :span="1.5">-->
            <!--          <el-button-->
            <!--            type="warning"-->
            <!--            plain-->
            <!--            icon="el-icon-download"-->
            <!--            size="mini"-->
            <!--            @click="handleExport"-->
            <!--            v-hasPermi="['iot:scenarioType:export']"-->
            <!--          >导出</el-button>-->
            <!--        </el-col>-->
            <!--        <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>-->
            <!--      </el-row>-->
        </el-card>

        <el-card class="card-wrap">
            <el-table v-if="refreshTable" v-loading="loading" :data="typeList" row-key="typeId" :default-expand-all="isExpandAll" :tree-props="{ children: 'children', hasChildren: 'hasChildren' }">
                <!--        <el-table-column type="selection" width="55" align="center"/>-->
                <!--        <el-table-column label="场景类型ID" align="center" prop="typeId"/>-->
                <el-table-column label="场景类型" align="center" prop="typeName" />
                <el-table-column label="备注" align="center" prop="remark" />
                <el-table-column label="创建时间" align="center" prop="createTime" width="300">
                    <template slot-scope="scope">
                        <span>{{ parseTime(scope.row.createTime) }}</span>
                    </template>
                </el-table-column>
                <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
                    <template slot-scope="scope">
                        <el-button size="small" type="success" style="padding: 5px" icon="el-icon-plus" @click="handleAdd(scope.row)" v-hasPermi="['system:scenarioType:add']">新增</el-button>
                        <el-button size="small" type="primary" style="padding: 5px" v-if="scope.row.parentId != 0" icon="el-icon-edit" @click="handleUpdate(scope.row)" v-hasPermi="['system:scenarioType:edit']">
                            编辑
                        </el-button>
                        <el-button size="small" type="danger" style="padding: 5px" v-if="scope.row.parentId != 0" icon="el-icon-delete" @click="handleDelete(scope.row)" v-hasPermi="['system:scenarioType:delete']">
                            编辑
                        </el-button>
                    </template>
                </el-table-column>
            </el-table>

            <!--      <pagination-->
            <!--        v-show="total>0"-->
            <!--        :total="total"-->
            <!--        :page.sync="queryParams.pageNum"-->
            <!--        :limit.sync="queryParams.pageSize"-->
            <!--        @pagination="getList"-->
            <!--      />-->

            <!-- 添加或修改场景类型对话框 -->
            <el-dialog :close-on-click-modal="false" :title="title" :visible.sync="open" width="500px" append-to-body>
                <el-form ref="form" :model="form" :rules="rules" label-width="80px">
                    <el-row>
                        <el-col :span="24" v-if="form.parentId !== 0">
                            <el-form-item label="上级机构" prop="parentId">
                                <treeselect v-model="form.parentId" :options="treeOptions" :normalizer="normalizer" disabled placeholder="选择上级机构" />
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-form-item label="场景类型" prop="typeName">
                        <el-input v-model="form.typeName" placeholder="请输入场景类型" />
                    </el-form-item>
                    <el-form-item label="备注" prop="remark">
                        <el-input v-model="form.remark" type="textarea" placeholder="请输入内容" />
                    </el-form-item>
                </el-form>
                <div slot="footer" class="dialog-footer">
                    <el-button type="primary" @click="submitForm">确 定</el-button>
                    <el-button @click="cancel">取 消</el-button>
                </div>
            </el-dialog>

            <!--      <el-table v-loading="loading" :data="typeList" @selection-change="handleSelectionChange">-->
            <!--        <el-table-column type="selection" width="55" align="center" />-->
            <!--        <el-table-column label="场景类型ID" align="center" prop="typeId" />-->
            <!--        <el-table-column label="场景类型" align="center" prop="typeName" />-->
            <!--        &lt;!&ndash;      <el-table-column label="状态" align="center" prop="status" />&ndash;&gt;-->
            <!--        <el-table-column label="备注" align="center" prop="remark" />-->
            <!--        <el-table-column label="操作" align="center" class-name="small-padding fixed-width">-->
            <!--          <template slot-scope="scope">-->
            <!--            <el-button-->
            <!--              size="mini"-->
            <!--              type="text"-->
            <!--              icon="el-icon-edit"-->
            <!--              @click="handleUpdate(scope.row)"-->
            <!--              v-hasPermi="['iotscenarioType:edit']"-->
            <!--            >修改</el-button>-->
            <!--            <el-button-->
            <!--              size="mini"-->
            <!--              type="text"-->
            <!--              icon="el-icon-delete"-->
            <!--              @click="handleDelete(scope.row)"-->
            <!--              v-hasPermi="['iotscenarioType:remove']"-->
            <!--            >删除</el-button>-->
            <!--          </template>-->
            <!--        </el-table-column>-->
            <!--      </el-table>-->
        </el-card>
    </div>
</template>

<script>
import { listType, getType, delType, addType, updateType, listExcludeChild, listAll } from '@/api/iot/scenarioType';
import Treeselect from '@riophae/vue-treeselect';
import '@riophae/vue-treeselect/dist/vue-treeselect.css';

export default {
    name: 'Type',
    components: { Treeselect },
    data() {
        return {
            // 是否展开，默认全部展开
            isExpandAll: true,
            // 重新渲染表格状态
            refreshTable: true,
            // 遮罩层
            loading: true,
            // 选中数组
            ids: [],
            // 非单个禁用
            single: true,
            // 非多个禁用
            multiple: true,
            // 显示搜索条件
            showSearch: true,
            // 总条数
            total: 0,
            // 场景类型表格数据
            typeList: [],
            // 树选项
            treeOptions: [],
            // 弹出层标题
            title: '',
            // 是否显示弹出层
            open: false,
            //场景类型选择列表
            typeNamedata: [],
            // 查询参数
            queryParams: {
                pageNum: 1,
                pageSize: 10,
                typeName: null,
                status: null,
            },
            // 表单参数
            form: {},
            // 表单校验
            rules: {
                typeName: [
                    { required: true, message: '场景类型不能为空', trigger: 'blur' },
                    {
                        pattern: /^.{1,25}$/,
                        message: '场景类型最多不超过25个字符',
                        trigger: 'blur',
                    },
                ],
            },
        };
    },
    created() {
        listAll().then((response) => {
            this.typeNamedata = response.data;
        });
        this.getList();
    },
    methods: {
        /** 查询场景类型列表 */
        getList() {
            this.loading = true;

            listType(this.queryParams).then((response) => {
                this.typeList = this.handleTree(response.data, 'typeId');
                this.loading = false;
            });
        },
        /** 转换机构数据结构 */
        normalizer(node) {
            if (node.children && !node.children.length) {
                delete node.children;
            }
            return {
                id: node.typeId,
                label: node.typeName,
                children: node.children,
            };
        },
        /** 展开/折叠操作 */
        toggleExpandAll() {
            this.refreshTable = false;
            this.isExpandAll = !this.isExpandAll;
            this.$nextTick(() => {
                this.refreshTable = true;
            });
        },
        // 取消按钮
        cancel() {
            this.open = false;
            this.reset();
        },
        // 表单重置
        reset() {
            this.form = {
                typeId: null,
                typeName: null,
                status: null,
                delFlag: null,
                createBy: null,
                createTime: null,
                updateBy: null,
                updateTime: null,
                remark: null,
            };
            this.resetForm('form');
        },
        /** 搜索按钮操作 */
        handleQuery() {
            this.queryParams.pageNum = 1;
            this.getList();
        },
        /** 重置按钮操作 */
        resetQuery() {
            this.resetForm('queryForm');
            this.handleQuery();
        },
        // 多选框选中数据
        handleSelectionChange(selection) {
            this.ids = selection.map((item) => item.typeId);
            this.single = selection.length !== 1;
            this.multiple = !selection.length;
        },
        /** 新增按钮操作 */
        handleAdd(row) {
            this.reset();
            this.open = true;
            this.title = '添加场景类型';
            if (row != undefined) {
                this.form.parentId = row.typeId;
            }
            console.log('row+++++++++:' + row);
            listType().then((response) => {
                this.treeOptions = this.handleTree(response.data, 'typeId');
            });
        },
        /** 修改按钮操作 */
        handleUpdate(row) {
            this.reset();
            if (row != undefined) {
                this.form.parentId = row.typeId;
            }
            getType(row.typeId).then((response) => {
                this.form = response.data;
                this.open = true;
                this.title = '修改场景类型';
                listExcludeChild(row.typeId).then((response) => {
                    this.treeOptions = this.handleTree(response.data, 'typeId');
                    if (this.treeOptions.length == 0) {
                        const noResultsOptions = { typeId: this.form.parentId, typename: this.form.typename, children: [] };
                        this.treeOptions.push(noResultsOptions);
                    }
                });
            });
        },
        /** 提交按钮 */
        submitForm() {
            this.$refs['form'].validate((valid) => {
                if (valid) {
                    if (this.form.typeId != null) {
                        updateType(this.form).then((response) => {
                            this.$modal.msgSuccess('修改成功');
                            this.open = false;
                            this.getList();
                        });
                    } else {
                        addType(this.form).then((response) => {
                            this.$modal.msgSuccess('新增成功');
                            this.open = false;
                            this.getList();
                        });
                    }
                }
            });
        },
        /** 删除按钮操作 */
        handleDelete(row) {
            const typeIds = row.typeId || this.ids;
            this.$modal
                .confirm('是否确认删除场景类型编号为"' + typeIds + '"的数据项？')
                .then(function () {
                    return delType(typeIds);
                })
                .then(() => {
                    this.getList();
                    this.$modal.msgSuccess('删除成功');
                })
                .catch(() => {});
        },
        /** 导出按钮操作 */
        handleExport() {
            this.download(
                'iot/scenarioType/export',
                {
                    ...this.queryParams,
                },
                `type_${new Date().getTime()}.xlsx`
            );
        },
    },
};
</script>
