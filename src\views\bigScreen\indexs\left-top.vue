<template>
    <div>
        <div class="container_equipment">
            <div style="color: #409eff">
                <dv-decoration-9 style="width: 4.8vw; height: 4.4vw" class="equipment" :dur="10" :color="['#409EFF', '#79bbff']">
                    <p style="font-weight: 600; font-size: 0.8vw">{{ deviceStatistic.deviceCount }}</p>
                </dv-decoration-9>
                <div style="text-align: center; margin-top: 1vh; font-weight: 600; font-size: 0.72vw">设备总数</div>
            </div>
            <div style="color: #3ca674">
                <dv-decoration-9 style="width: 4.8vw; height: 4.4vw" class="equipment" :dur="12" :color="['#3CA674', '#95d475']">
                    <p style="font-weight: 600; font-size: 0.8vw">{{ deviceStatistic.deviceOnlineCount }}</p>
                </dv-decoration-9>
                <div style="text-align: center; margin-top: 1vh; font-weight: 600; font-size: 0.72vw">在线设备</div>
            </div>
            <div style="color: #e6a23c">
                <dv-decoration-9 style="width: 4.8vw; height: 4.4vw" class="equipment" :dur="14" :color="['#E6A23C', '#eebe77']">
                    <p style="font-weight: 600; font-size: 0.8vw">{{ deviceStatistic.deviceCount - deviceStatistic.deviceOnlineCount }}</p>
                </dv-decoration-9>
                <div style="text-align: center; margin-top: 1vh; font-weight: 600; font-size: 0.72vw">离线设备</div>
            </div>
            <div style="color: #ee6666">
                <dv-decoration-9 style="width: 4.8vw; height: 4.4vw" class="equipment" :dur="16" :color="['#EE6666', '#f89898']">
                    <p style="font-weight: 600; font-size: 0.8vw">{{ deviceStatistic.alertCount }}</p>
                </dv-decoration-9>
                <div style="text-align: center; margin-top: 1vh; font-weight: 600; font-size: 0.72vw">告警数量</div>
            </div>
        </div>
        <!-- <div class="equipment_statistics">
            <div class="numberdevices">
                <div class="devicesicon">
                    <svg t="1713317677271" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="4338" width="200" height="200"><path d="M162.9 827.9L82 901.8V530.2l80.9 20.6z" fill="#1296db" p-id="4339"></path><path d="M82 919.2c-2.4 0-4.7-0.5-7-1.5-6.3-2.8-10.4-9-10.4-15.9V530.2c0-5.4 2.5-10.4 6.7-13.7 4.2-3.3 9.7-4.4 14.9-3.1l80.9 20.7c7.7 2 13.1 8.9 13.1 16.8V828c0 4.9-2 9.5-5.6 12.8l-80.9 73.9c-3.2 2.9-7.4 4.5-11.7 4.5z m17.4-366.7v309.9l46.2-42.2v-256l-46.2-11.7z" fill="#1296db" p-id="4340"></path><path d="M741.5 694.5L207 395.2c-20.7-11.6-27.6-38.8-15.4-60.6L297.2 146c12.2-21.9 39-30.2 59.7-18.6l579.7 329.1-195.1 238z" fill="#1296db" p-id="4341"></path><path d="M741.5 711.8c-2.9 0-5.8-0.7-8.5-2.2L198.5 410.3c-14.2-8-24.3-21.2-28.4-37.3-4-15.8-1.8-32.4 6.4-46.9l105.6-188.6c8.1-14.5 21.1-25.1 36.7-29.9 15.9-4.9 32.5-3.3 46.7 4.7l579.8 329.2c4.4 2.5 7.6 6.9 8.5 11.9 0.9 5-0.4 10.2-3.6 14.2L754.9 705.5c-3.4 4.1-8.3 6.3-13.4 6.3zM336.8 139.5c-2.6 0-5.2 0.4-7.7 1.2-7 2.2-12.9 7.1-16.7 13.7L206.8 343c-3.7 6.7-4.8 14.3-3 21.4 1.7 6.8 5.9 12.3 11.7 15.6l521.8 292.2 172.8-210.8-561.7-318.9c-3.6-1.9-7.5-3-11.6-3z" fill="#1296db" p-id="4342"></path><path d="M686.1 726.8l-460.7-258c-9.1-5.1-12.3-16.6-7.2-25.6l18.1-32.3 505.2 282.9-11.6 20.7c-8.7 15.5-28.3 21-43.8 12.3z" fill="#1296db" p-id="4343"></path><path d="M692.6 739.8c-12.4 0-24.6-3.2-35.7-9.4L229 490.8c-11.7-6.5-20.1-17.2-23.7-30.1-3.6-12.9-2-26.4 4.5-38l11.3-20.2c4.7-8.4 15.3-11.3 23.6-6.7l505.2 282.9c4 2.2 7 6 8.2 10.4 1.3 4.4 0.7 9.2-1.6 13.2-9.6 17.1-25.2 29.4-44 34.7-6.5 1.8-13.2 2.8-19.9 2.8zM242.9 434.5l-2.9 5.1c-2 3.6-2.5 7.7-1.4 11.7 1.1 3.9 3.7 7.2 7.3 9.2l427.9 239.6c13.1 7.3 28.7 6.2 40.4-1.7L242.9 434.5z" fill="#1296db" p-id="4344"></path><path d="M412.6 576.4l-53.7-31.1L289 666.4H168.7v62h156.1z" fill="#1296db" p-id="4345"></path><path d="M324.8 745.8H168.7c-9.6 0-17.4-7.8-17.4-17.4v-62c0-9.6 7.8-17.4 17.4-17.4H279l64.9-112.4c4.8-8.3 15.4-11.1 23.7-6.4l53.7 31c8.3 4.8 11.1 15.4 6.4 23.7l-87.5 151.5c-3 5.6-8.7 9.4-15.4 9.4z m-138.7-34.7h128.7l74.1-128.4-23.7-13.7L304 675c-3.1 5.4-8.8 8.7-15 8.7H186.1v27.4z" fill="#1296db" p-id="4346"></path><path d="M358.3 241m-21.8 0a21.8 21.8 0 1 0 43.6 0 21.8 21.8 0 1 0-43.6 0Z" fill="#1296db" p-id="4347"></path><path d="M786.8 733.1c-3.9 0-7.9-1.3-11.1-4-7.4-6.1-8.3-17.1-2.2-24.4L928.7 519c6.1-7.4 17.1-8.3 24.4-2.2 7.4 6.1 8.3 17.1 2.2 24.4L800.1 726.8c-3.4 4.2-8.4 6.3-13.3 6.3z" fill="#1296db" p-id="4348"></path></svg>
                </div>
                <div class="devicesname">视频监控</div>
                <div class="devicescount">200</div>
            </div>
            <div class="numberdevices">
                <div class="devicesicon">
                    <svg t="1713420481834" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="4288" width="200" height="200"><path d="M272 784a32 32 0 1 1 64 0v32a128 128 0 0 1-128 128 32 32 0 1 1 0-64 64 64 0 0 0 64-64z m480 0v32a64 64 0 0 0 64 64 32 32 0 0 1 0 64 128 128 0 0 1-128-128v-32a32 32 0 1 1 64 0zM80 64h864a64 64 0 0 1 64 64v464a64 64 0 0 1-64 64H80a64 64 0 0 1-64-64V128a64 64 0 0 1 64-64zM128 512a32 32 0 1 0 0 64h752a32 32 0 0 0 0-64z m0-112a32 32 0 1 0 0 64h752a32 32 0 1 0 0-64z m464-256a32 32 0 1 0 0 64h64a32 32 0 1 0 0-64z m160 0a32 32 0 1 0 0 64h64a32 32 0 0 0 0-64zM512 736a32 32 0 0 1 32 32v160a32 32 0 0 1-64 0V768a32 32 0 0 1 32-32z m0 0" fill="#1296db" p-id="4289"></path></svg>
                </div>
                <div class="devicesname">暖通空调</div>
                <div class="devicescount">80</div>
            </div>
            <div class="numberdevices">
                <div class="devicesicon">
                    <svg t="1713319054917" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="8406" width="200" height="200"><path d="M855.5008 517.7856h-112.8448l-10.2912-422.2976a27.0848 27.0848 0 1 0-54.1696 0l-8.5504 422.2976H344.3712l-10.24-422.2976a27.136 27.136 0 1 0-54.272 0L271.36 517.7856H158.5152a49.7664 49.7664 0 0 0-49.7664 49.7664v348.5184c0 27.4944 22.272 49.7664 49.7664 49.7664h696.9856a49.7664 49.7664 0 0 0 49.7152-49.7664v-348.5184a49.7664 49.7664 0 0 0-49.7152-49.7664zM233.216 667.136a24.8832 24.8832 0 1 1 0-49.7664 24.8832 24.8832 0 0 1 0 49.7664z m99.5328 0a24.8832 24.8832 0 1 1 0-49.7664 24.8832 24.8832 0 0 1 0 49.7664z m99.584 0a24.9344 24.9344 0 1 1 0.0512-49.8176 24.9344 24.9344 0 0 1-0.0512 49.8176z m273.7664 223.9488a24.8832 24.8832 0 1 1-49.8176 0v-99.4304a25.088 25.088 0 0 1 25.0368-24.9856c13.7216 0 24.832 11.1104 24.832 24.9344v99.4816z m99.584 0a24.8832 24.8832 0 1 1-49.7664 0v-99.4304a24.9344 24.9344 0 1 1 49.8176-0.0512v99.4816z" fill="#00A0E9" p-id="8407"></path></svg>
                </div>
                <div class="devicesname">网关</div>
                <div class="devicescount">80</div>
            </div>
            <div class="numberdevices">
                <div class="devicesicon">
                    <svg t="1713420734543" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="16435" width="200" height="200"><path d="M998.4 938.667h-31.289V85.333H998.4c14.222 0 25.6-14.222 25.6-28.444s-11.378-28.445-25.6-28.445H25.6C11.378 28.444 0 42.667 0 56.89s11.378 28.444 25.6 28.444h31.289v853.334H25.6c-14.222 0-25.6 14.222-25.6 28.444s11.378 28.445 25.6 28.445h969.956c14.222 0 25.6-14.223 25.6-28.445 2.844-14.222-8.534-28.444-22.756-28.444z m-88.178 0H540.444V85.333h369.778v853.334zM113.778 85.333h369.778v853.334H113.778V85.333z" fill="#1296db" p-id="16436"></path><path d="M284.444 369.778l-85.333 85.333H256v227.556h56.889V455.11h56.889z m455.112 312.889l85.333-85.334H768V369.778h-56.889v227.555h-56.889z" fill="#1296db" p-id="16437"></path></svg>
                </div>
                <div class="devicesname">电梯</div>
                <div class="devicescount">1290</div>
            </div>
        </div> -->
    </div>
</template>
<style scoped>
.container_equipment {
    display: flex;
    text-align: center;
    justify-content: center;
    flex-wrap: wrap;
    /* justify-content: space-around; 均匀分配空间，两边留白 */
    /* 或者使用justify-content: space-between; 仅在子元素间留白 */
    /* width: 20vw; */
    /* 为了让内部div占据总宽度的20%，我们需要先计算出总的份数 */
    /* 四个div总共占80vw (因为20vw给了所有间隔) */
    /* flex-wrap: nowrap; 不换行 */
    /* height: 10vh; */
}

.container_equipment .equipment {
    font-weight: 900;
    box-sizing: border-box; /* 包含内边距与边框在宽高之内 */
}

.equipment_statistics {
    width: 20vw;
    height: 14vh;
    margin-top: 0.4vh;
    display: grid;
    grid-template-columns: 1fr 1fr; /* 表示两列，每列宽度相等 */
}

.equipment_statistics .numberdevices {
    width: 8.32vw;
    height: 5vh;
    float: left;
    margin: 0.36vw 0.8vw 0 0.8vw;
    border: 1px solid #c6e2ff;
    border-radius: 0.4vw; /* 所有角均为10像素的圆角 */
}
.equipment_statistics .numberdevices .devicesicon {
    width: 1.8vw;
    height: 3.4vh;
    float: left;
    margin: 0.8vh 0.8vh 0.8vh 0.8vh;
}

.equipment_statistics .numberdevices .devicesicon svg {
    width: 1.8vw;
    height: 3.4vh;
    transition: transform 0.3s ease-in-out; /* 添加过渡效果，使放大过程更平滑 */
}

.equipment_statistics .numberdevices .devicesicon svg:hover {
    transform: scale(1.2); /* 鼠标悬停时放大到原尺寸的1.2倍 */
    cursor: pointer;
}

.equipment_statistics .numberdevices .devicesname {
    width: 3.2vw;
    height: 2.5vh;
    float: right;
    color: #fafcff;
    font-size: 0.68vw;
    line-height: 2.88vh;
    font-weight: 600;
}

.equipment_statistics .numberdevices .devicescount {
    width: 3.2vw;
    height: 2.5vh;
    float: right;
    font-size: 0.74vw;
    font-weight: 700;
    color: #1296db;
    line-height: 2.6vh;
}
</style>

<script>
import { getDeviceStatistic } from '@/api/iot/device';
export default {
    data() {
        return {
            // 设备统计信息
            deviceStatistic: {
                deviceCount: 100,
                productCount: 100,
                eventCount: 100,
                alertCount: 100,
                deviceOnlineCount: 100,
            },
            timer: null,
        };
    },
    created() {
        this.getDeviceStatic();
    },
    beforeDestroy() {
        this.clearData();
    },
    methods: {
        clearData() {
            if (this.timer) {
                clearInterval(this.timer);
                this.timer = null;
            }
        },
        getDeviceStatic() {
            getDeviceStatistic().then((response) => {
                if (response.code == 200) {
                    this.deviceStatistic = response.data;
                    // 轮询
                    this.switper();
                }
            });
        },
        //轮询
        switper() {
            if (this.timer) {
                return;
            }
            let looper = (a) => {
                this.getDeviceStatic();
            };
            this.timer = setInterval(looper, 60000);
        },
    },
};
</script>
