import request from '@/utils/request'

// 查询场景类型列表
export function listType(query) {
  return request({
    url: '/iot/scenarioType/list',
    method: 'get',
    params: query
  })
}

// 查询场景类型详细
export function getType(typeId) {
  return request({
    url: '/iot/scenarioType/' + typeId,
    method: 'get'
  })
}

// 新增场景类型
export function addType(data) {
  return request({
    url: '/iot/scenarioType',
    method: 'post',
    data: data
  })
}

// 修改场景类型
export function updateType(data) {
  return request({
    url: '/iot/scenarioType',
    method: 'put',
    data: data
  })
}

// 删除场景类型
export function delType(typeId) {
  return request({
    url: '/iot/scenarioType/' + typeId,
    method: 'delete'
  })
}

// 查询场景类型列表
export function listAll(query) {
  return request({
    url: '/iot/scenarioType/listAll',
    method: 'get',
    params: query
  })
}

// 查询场景类型列表
export function listScenarioTypeTree(query) {
  return request({
    url: '/iot/scenarioType/scenarioTypeTree',
    method: 'get',
    params: query
  })
}
// 查询机构列表（排除节点）
export function listExcludeChild(typeId) {
  return request({
    url: '/iot/scenarioType/list/exclude/' + typeId,
    method: 'get',
  });
}

