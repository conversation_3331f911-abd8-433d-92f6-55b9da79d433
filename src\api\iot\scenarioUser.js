import request from '@/utils/request'

// 查询场景设备列表
export function listScenarioUser(query) {
  return request({
    url: '/iot/scenarioUser/list',
    method: 'get',
    params: query
  })
}

// 查询场景设备详细
export function getScenarioUser(id) {
  return request({
    url: '/iot/scenarioUser/' + id,
    method: 'get'
  })
}

// 新增场景设备
export function addScenarioUser(data) {
  return request({
    url: '/iot/scenarioUser',
    method: 'post',
    data: data
  })
}

// 修改场景设备
export function updateScenarioUser(data) {
  return request({
    url: '/iot/scenarioUser',
    method: 'put',
    data: data
  })
}

// 删除场景设备
export function delScenarioUser(id) {
  return request({
    url: '/iot/scenarioUser/' + id,
    method: 'delete'
  })
}
