<template>
    <el-dialog :close-on-click-modal="false" title="场景设备" :visible.sync="open" width="800px" append-to-body>
        <el-form :model="queryParams" ref="queryForm" :inline="true" label-width="68px" style="margin-bottom: -10px">
            <el-form-item label="设备名称" prop="deviceName">
                <el-input v-model="queryParams.deviceName" placeholder="请输入设备名称" size="mini" clearable
                    @keyup.enter.native="handleQuery" />
            </el-form-item>
            <!--            <el-form-item label="渠道类型" prop="channelType">-->
            <!--                <el-select v-model="queryParams.channelType" placeholder="请选择渠道类型" clearable size="mini" style="width: 100%">-->
            <!--                    <el-option v-for="option in dict.type.notify_channel_type" :key="option.value" :label="option.label" :value="option.value"></el-option>-->
            <!--                </el-select>-->
            <!--            </el-form-item>-->
            <el-form-item>
                <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
                <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
            </el-form-item>
        </el-form>

        <el-table v-loading="loading" :data="deviceList" @select="handleSelectionChange"
            @select-all="handleSelectionAll" ref="multipleTable" size="mini">
            <el-table-column type="selection" width="40"></el-table-column>
            <el-table-column label="编号" width="200" align="center" prop="serialNumber" />
            <el-table-column label="设备名称" align="center" prop="deviceName" min-width="100" />
            <!--            <el-table-column label="设备编号" align="center" prop="serialNumber"></el-table-column>-->
            <el-table-column label="所属产品" align="center" prop="productName"></el-table-column>
            <el-table-column label="是否启用" align="center" prop="status">
                <template slot-scope="scope">
                    <el-switch v-model="scope.row.status" @change="handleStatus(scope.row)" disabled :active-value="1"
                        :inactive-value="0" />
                </template>
            </el-table-column>
        </el-table>

        <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum"
            :limit.sync="queryParams.pageSize" @pagination="getList" />

        <div slot="footer" style="width: 100%">
            <el-button type="primary" @click="handleEmitData">确 定</el-button>
            <el-button @click="cancel">取 消</el-button>
        </div>
    </el-dialog>
</template>

<script>
import { listDeviceShort } from '@/api/iot/device';

export default {
    name: 'deviceList',
    data() {
        return {
            // 通知模板列表数据
            deviceList: [],
            // 选中的通知模板IDs
            ids: [],
            // 选中的通知模板
            selects: [],
            // 加载
            loading: true,
            // 总条数
            total: 0,
            // 是否显示弹出层
            open: false,
            // 查询参数
            queryParams: {
                pageNum: 1,
                pageSize: 10,
                deviceName: null,
                deptId: null,
            },
        };
    },
    created() { },
    methods: {
        /** 确定后返回数据 */
        handleEmitData() {
            this.$emit('deviceEvent', this.selects);
            this.open = false;
        },
        /** 查询通知模版列表 */
        getList() {
            this.loading = true;
            listDeviceShort(this.queryParams).then((response) => {
                this.deviceList = response.rows;
                this.total = response.total;
                this.loading = false;
                // 设置场景选中
                this.deviceList.forEach((row) => {
                    this.$nextTick(() => {
                        if (this.ids.some((x) => x === row.serialNumber)) {
                            this.$refs.multipleTable.toggleRowSelection(row, true);
                        }
                    });
                });
            });
        },
        // 取消按钮
        cancel() {
            this.open = false;
            this.ids = [];
            this.selects = [];
        },
        // 搜索按钮操作
        handleQuery() {
            this.queryParams.pageNum = 1;
            this.getList();
        },
        // 重置按钮操作
        resetQuery() {
            this.resetForm('queryForm');
            this.handleQuery();
        },
        // 多选框选中数据
        handleSelectionChange(selection, row) {
            // ID是否存在于原始设备ID数组中
            let index = this.ids.indexOf(row.serialNumber);
            // 是否选中
            let value = selection.indexOf(row);
            if (index == -1 && value != -1) {
                // 不存在且选中
                this.ids.push(row.serialNumber);
                this.selects.push(row);
            } else if (index != -1 && value == -1) {
                // 存在且取消选中
                this.ids.splice(index, 1);
                this.selects.splice(index, 1);
            }
        },
        // 全选事件处理
        handleSelectionAll(selection) {
            for (let i = 0; i < this.deviceList.length; i++) {
                // ID是否存在于原始设备ID数组中
                let index = this.ids.indexOf(this.deviceList[i].id);
                // 是否选中
                let value = selection.indexOf(this.deviceList[i]);
                if (index == -1 && value != -1) {
                    // 不存在且选中
                    this.ids.push(this.deviceList[i].serialNumber);
                    this.selects.push(this.deviceList[i]);
                } else if (index != -1 && value == -1) {
                    // 存在且取消选中
                    this.ids.splice(index, 1);
                    this.selects.splice(index, 1);
                }
            }
        },
    },
};
</script>

<style lang="scss" scoped></style>
