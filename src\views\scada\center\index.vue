<template>
    <div class="center-wrap">
        <el-form @submit.native.prevent :model="queryParams" ref="queryForm" size="small" :inline="true"
            v-show="showSearch" label-width="48px">
            <el-form-item label="名称" prop="pageName">
                <el-input v-model="queryParams.pageName" placeholder="请输入页面名称" clearable
                    @keyup.enter.native="handleQuery" />
            </el-form-item>
            <el-form-item>
                <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
                <el-button icon="el-icon-refresh" size="mini" @click="handleResetQuery">重置</el-button>
            </el-form-item>
        </el-form>

        <el-row :gutter="10" class="mb10">
            <el-col :span="1.5">
                <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="handleAdd"
                    v-hasPermi="['scada:center:add']">新增</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="success" plain icon="el-icon-edit" size="mini" :disabled="single" @click="handleUpdate"
                    v-hasPermi="['scada:center:edit']">修改</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="danger" plain icon="el-icon-delete" size="mini" :disabled="multiple"
                    @click="handleDelete" v-hasPermi="['scada:center:remove']">删除</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="warning" plain icon="el-icon-download" size="mini" @click="handleExport"
                    v-hasPermi="['scada:center:export']">导出</el-button>
            </el-col>
            <template>
                <el-tooltip effect="dark" content="切换卡片/列表" placement="top" style="float: right;">
                    <el-button size="mini" circle icon="el-icon-s-grid" @click="handleChangeShowType" />
                </el-tooltip>
            </template>
            <right-toolbar :showSearch.sync="showSearch" @queryTable="getList">
            </right-toolbar>
        </el-row>

        <div v-if="showType == 'card'">
            <el-row :gutter="15" v-loading="loading">
                <el-checkbox-group v-model="ids" @change="checkboxChange">
                    <el-col :xs="24" :sm="12" :md="8" :lg="6" :xl="6" style="margin-top: 7.5px; margin-bottom: 7.5px"
                        v-for="item in centerList" :key="item.id">
                        <el-card class="card-wrap" :body-style="{ padding: '10px' }">
                            <div class="img-wrap">
                                <el-image style="width: 100%; height: 100%; border-radius: 5px" lazy
                                    :src="baseApi + item.pageImage" fit="cover" v-hasPermi="['scada:center:query']"
                                    @click="goToDetail(item)"></el-image>
                            </div>
                            <div class="tag-wrap">
                                <span>{{ item.pageResolution ? item.pageResolution : '未知' }}</span>
                            </div>
                            <div class="name-wrap">
                                <span>{{ item.pageName }}</span>
                            </div>
                            <div class="tools-wrap">
                                <el-checkbox class="checkbox" :label="item.id" :key="item.id"><span
                                        v-show="false">占位符</span></el-checkbox>
                                <div class="right-wrap">
                                    <!-- 二期开发 -->
                                    <!-- <i class="el-icon-share" style="color: #e6a23c" @click="handleShare(item)"></i> -->
                                    <el-dropdown style="margin-left: 8px">
                                        <!-- <span class="el-dropdown-link">
                                            <svg-icon style="fill: #1890ff" icon-class="more-vertical"></svg-icon>
                                        </span> -->
                                        <!-- <el-dropdown-menu slot="dropdown"> -->
                                        <!-- <el-dropdown-item command="edit"> -->
                                        <el-button size="mini" type="text" icon="el-icon-view"
                                            @click="goToDetail(item)">设计</el-button>
                                        <!-- </el-dropdown-item> -->
                                        <!-- <el-dropdown-item command="preview"> -->
                                        <el-button style="color: #e6a23c" type="text" size="mini" icon="el-icon-view"
                                            @click="handlePreview(item)">预览</el-button>
                                        <!-- </el-dropdown-item> -->
                                        <!-- <el-dropdown-item command="remove"> -->
                                        <el-button style="color: #f56c6c" size="mini" type="text" icon="el-icon-delete"
                                            @click="handleDelete(item)">删除</el-button>
                                        <!-- </el-dropdown-item> -->
                                        <!-- </el-dropdown-menu> -->
                                    </el-dropdown>
                                </div>
                            </div>
                        </el-card>
                    </el-col>
                </el-checkbox-group>
            </el-row>
            <el-empty description="暂无数据" v-if="total == 0"></el-empty>
            <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum"
                :limit.sync="queryParams.pageSize" @pagination="getList" />
        </div>
        <div v-if="showType == 'list'">
            <el-table v-loading="loading" :data="centerList" @selection-change="handleSelectionChange">
                <el-table-column type="selection" width="55" align="center" />
                <el-table-column label="id" align="center" prop="id" width="100" />
                <el-table-column label="名称" align="center" prop="pageName" />
                <el-table-column label="分辨率" align="center" prop="pageResolution" width="120">
                    <template slot-scope="scope">
                        <span>{{ scope.row.pageResolution ? scope.row.pageResolution : '未知' }}</span>
                    </template>
                </el-table-column>
                <el-table-column label="封面" align="center" prop="pageImage" width="120">
                    <template slot-scope="scope">
                        <image-preview :src="scope.row.pageImage" :width="50" :height="50" />
                    </template>
                </el-table-column>
                <!-- 二期开发 -->
                <!-- <el-table-column label="分享" align="center" prop="share" width="120">
                    <template slot-scope="scope">
                        <el-image style="width: 50px; height: 50px" :src="scope.row.share" fit="fit" @click="handleShare(scope.row)"></el-image>
                    </template>
                </el-table-column> -->
                <el-table-column label="更新时间" align="center" prop="updateTime" width="180" />
                <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="180">
                    <template slot-scope="scope">
                        <el-button size="mini" type="text" icon="el-icon-view" @click="goToDetail(scope.row)"
                            v-hasPermi="['scada:center:query']">详情</el-button>
                        <el-button style="color: #e6a23c" size="mini" type="text" icon="el-icon-view"
                            @click="handlePreview(item)" v-hasPermi="['scada:center:preview']">预览</el-button>
                        <el-button style="color: #f56c6c" size="mini" type="text" icon="el-icon-delete"
                            @click="handleDelete(scope.row)" v-hasPermi="['scada:center:remove']">删除</el-button>
                    </template>
                </el-table-column>
            </el-table>
            <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum"
                :limit.sync="queryParams.pageSize" @pagination="getList" />
        </div>

        <!-- 添加或修改组态信息对话框 -->
        <el-dialog :close-on-click-modal="false" :title="dialog.title" :visible.sync="dialog.open" width="400px"
            append-to-body>
            <div class="el-divider el-divider--horizontal" style="margin-top: -25px"></div>
            <el-form ref="dialogForm" :model="dialog.form" :rules="dialog.rules" label-width="65px">
                <el-form-item label="封面" prop="pageImage">
                    <image-upload v-model="dialog.form.pageImage" :multiple="false"
                        :class="{ disable: uploadDisabled }" />
                </el-form-item>
                <el-form-item label="名称" prop="pageName">
                    <el-input v-model="dialog.form.pageName" placeholder="请输入名称" clearable />
                </el-form-item>
                <el-form-item label="描述" prop="remark">
                    <el-input v-model="dialog.form.remark" placeholder="请输入描述" clearable />
                </el-form-item>
            </el-form>
            <div slot="footer" class="dialog-footer">
                <el-button type="primary" @click="handleDialogSubmit">确 定</el-button>
                <el-button @click="handleDialogCancel">取 消</el-button>
            </div>
        </el-dialog>
    </div>
</template>

<script>
import { listCenter, getCenter, delCenter, addCenter, updateCenter } from '@/api/scada/center';

export default {
    name: 'Center',
    dicts: ['sys_page_size'],
    computed: {
        uploadDisabled: function () {
            return this.dialog.form.pageImage !== '';
        },
    },
    data() {
        return {
            loading: true, // 遮罩层
            baseApi: process.env.VUE_APP_BASE_API,
            single: true, // 非单个禁用
            multiple: true, // 非多个禁用
            showSearch: true, // 显示搜索条件
            // 查询参数
            queryParams: {
                pageNum: 1,
                pageSize: 10,
                pageName: null,
            },
            ids: [], // 选中数组
            centerList: [], // 组态中心表格数据
            total: 0, // 总条数
            showType: 'card', // 展示方式
            dialog: {
                open: false, // 弹出层标题
                title: '', // 对话框标题
                // 表单参数
                form: {
                    pageImage: '',
                    pageName: '',
                    remark: '',
                },
                // 表单校验
                rules: {
                    pageName: [{ required: true, message: '请输入名称', trigger: 'change' }],
                },
            },
        };
    },
    mounted() {
        this.$busEvent.$on('updateCenter', () => {
            this.getList();
        });
        this.getList();
    },
    methods: {
        // 查询组态中心列表
        getList() {
            this.loading = true;
            listCenter(this.queryParams).then((response) => {
                if (response.code === 200) {
                    this.centerList = response.rows;
                    this.total = response.total;
                }
                this.loading = false;
            });
        },
        // 搜索按钮操作
        handleQuery() {
            this.ids = [];
            this.queryParams.pageNum = 1;
            this.getList();
        },
        // 重置按钮操作
        handleResetQuery() {
            this.ids = [];
            this.resetForm('queryForm');
            this.handleQuery();
        },
        // 新增按钮操作
        handleAdd() {
            this.reset();
            this.dialog.open = true;
            this.dialog.title = '添加组态信息';
        },
        // 表单重置
        reset() {
            this.dialog.form = {
                pageImage: '',
                pageName: '',
                remark: '',
            };
            this.resetForm('dialogForm');
        },
        // 修改按钮操作
        handleUpdate(row) {
            this.dialog.title = '修改组态信息';
            const id = row.id || this.ids;
            getCenter(id).then((res) => {
                if (res.code === 200) {
                    this.dialog.form = res.data;
                    this.dialog.open = true;
                }
            });
        },
        // 提交按钮
        handleDialogSubmit() {
            this.$refs['dialogForm'].validate((valid) => {
                if (valid) {
                    if (this.dialog.form.id != null) {
                        updateCenter(this.dialog.form).then((res) => {
                            if (res.code === 200) {
                                this.$modal.msgSuccess('修改成功');
                                this.dialog.open = false;
                                this.getList();
                            }
                        });
                    } else {
                        addCenter(this.dialog.form).then((res) => {
                            if (res.code === 200) {
                                this.$modal.msgSuccess('新增成功');
                                this.dialog.open = false;
                                this.getList();
                            }
                        });
                    }
                }
            });
        },
        // 取消按钮
        handleDialogCancel() {
            this.dialog.open = false;
        },
        // 删除按钮操作
        handleDelete(row) {
            const ids = row.id || this.ids;
            this.$modal
                .confirm('是否确认删除组态编号为"' + ids + '"的数据项？')
                .then(() => {
                    this.loading = true;
                    return delCenter(ids);
                })
                .then(() => {
                    this.loading = true;
                    this.getList();
                    this.$modal.msgSuccess('删除成功');
                })
                .catch(() => { });
        },
        // 跳转组态详情
        goToDetail(row) {
            this.$router.push({
                path: '/scada/topo/editor',
                query: {
                    id: row.id,
                    guid: row.guid,
                },
            });
        },
        // 分享
        handleShare(row) {
            this.$alert('二期开发', '提示', {
                confirmButtonText: '确定',
            });
        },
        // 跳转预览详情
        handlePreview(row) {
            const routeUrl = this.$router.resolve({
                path: '/topo/fullscreen',
                query: {
                    id: row.id,
                    guid: row.guid,
                },
            });
            window.open(routeUrl.href, '_blank');
        },
        // 多选框选中数据
        handleSelectionChange(selection) {
            this.ids = selection.map((item) => item.id);
            this.single = selection.length !== 1;
            this.multiple = !selection.length;
        },
        // 切换显示方式
        handleChangeShowType() {
            this.ids = [];
            this.showType = this.showType == 'card' ? 'list' : 'card';
        },
        // 导出按钮操作
        handleExport() {
            this.download(
                'scada/center/export',
                {
                    ...this.queryParams,
                },
                `组态${new Date().getTime()}.xlsx`
            );
        },
        // 卡片选择
        checkboxChange(selection) {
            this.single = selection.length != 1;
            this.multiple = !selection.length;
        },
    },
};
</script>

<style lang="scss" scoped>
.center-wrap {
    padding: 20px;

    .card-wrap {
        position: relative;
        border-radius: 5px;

        .img-wrap {
            height: 200px;
            width: 100%;
            // transition: transform 0.3s ease;

            // &:hover {
            //     cursor: pointer;
            //     transform: scale(1.1);
            // }
        }

        .tag-wrap {
            position: absolute;
            top: 0;
            left: 0;
            background-color: #1890ff;
            border-top-left-radius: 5px;
            border-bottom-right-radius: 5px;
            padding: 5px 15px;
            font-size: 12px;
            color: #fff;
        }

        .name-wrap {
            height: 20px;
            line-height: 20px;
            margin-top: 10px;
            font-size: 14px;
        }

        .tools-wrap {
            margin-top: 10px;
            display: flex;
            flex-direction: row;
            justify-content: space-between;
            align-items: center;

            .right-wrap {
                display: flex;
                flex-direction: row;
                align-items: center;
                font-size: 13px;
                cursor: pointer;
            }
        }
    }
}

.disable {
    ::v-deep .el-upload--picture-card {
        display: none !important;
    }
}
</style>
