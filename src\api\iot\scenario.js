import request from '@/utils/request'

// 查询场景列表
export function listScenario(query) {
  return request({
    url: '/iot/scenario/list',
    method: 'get',
    params: query
  })
}

// 查询场景详细
export function getScenario(scenarioId) {
  return request({
    url: '/iot/scenario/' + scenarioId,
    method: 'get'
  })
}

// 新增场景
export function addScenario(data) {
  return request({
    url: '/iot/scenario',
    method: 'post',
    data: data
  })
}

// 修改场景
export function updateScenario(data) {
  return request({
    url: '/iot/scenario',
    method: 'put',
    data: data
  })
}

// 删除场景
export function delScenario(scenarioId) {
  return request({
    url: '/iot/scenario/' + scenarioId,
    method: 'delete'
  })
}

// 查询场景类型列表
export function listAllScenario(query) {
  return request({
    url: '/iot/scenario/listAll',
    method: 'get',
    params: query
  })
}
