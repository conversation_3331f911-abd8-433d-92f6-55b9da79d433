<template>
    <div style="padding: 6px">
        <!-- 查询表单卡片 -->
        <el-card v-show="showSearch" style="margin-bottom: 5px">
            <search-form ref="searchForm" :initQuery="queryParams" @search="handleSearch" @reset="handleReset" />
        </el-card>

        <!-- 数据表格卡片 -->
        <el-card style="padding-bottom: 100px" title="曲线趋势图">
            <template #header>
                <div class="card-header">
                    <span>曲线趋势图</span>
                </div>
            </template>

            <div>
                <div style="width: 100%; height: 420px; margin-bottom: 50px" ref="TrendChart"></div>
            </div>

            <el-table v-if="showTable" v-loading="loading" class="no-divider-table" :data="tableData">
                <!-- 新增：更新时间列 -->
                <el-table-column label="更新时间" width="300">
                    <template #default="scope">
                        {{ chart_x[scope.$index] }}
                    </template>
                </el-table-column>

                <!-- 动态生成表格列 -->
                <el-table-column v-for="(column, index) in columns" :key="index" :label="column" align="center">
                    <template #default="scope">
                        {{ scope.row[column] }}
                    </template>
                </el-table-column>
            </el-table>

            <!-- 分页组件 -->
            <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize" @pagination="getList" />
        </el-card>
    </div>
</template>

<script>
import { getModelData } from '@/api/customize/histroy';
import SearchForm from '@/views/dataManager/components/searchForm';
import * as echarts from 'echarts';

export default {
    name: 'DeviceHistoryData',
    components: {
        SearchForm,
    },
    data() {
        return {
            //趋势图
            chart: null,
            chart_x: [],
            chart_y: [],
            // 遮罩层
            loading: false,
            // 总条数
            total: 0,
            // 历史数据表格数据
            showTable: false,
            result: {},
            tableData: [], // 新增：用于存储表格数据的数组
            counmnData: [],
            dataList: [],
            // 显示搜索条件
            showSearch: true,
            // 查询参数
            queryParams: {
                pageNum: 1,
                pageSize: 10,
                serialNumber: undefined,
                identityList: [],
                beginTime: undefined,
                endTime: undefined,
            },
            // 表格列
            columns: [],
        };
    },
    mounted() {
        this.$nextTick(() => {
            // 确保 DOM 更新完成
            this.initChart();
        });
    },
    methods: {
        /** 初始构造趋势图 */
        initChart() {
            if (!this.$refs.TrendChart) return;
            this.chart = echarts.init(this.$refs.TrendChart);
        },
        /** 查询历史数据列表 */
        getList() {
            this.loading = true;

            getModelData(this.queryParams).then((response) => {
                this.dataList = response.rows;
                this.total = response.total;
                this.loading = false;
                this.DrawChart();
            });
        },
        /** 处理搜索表单提交 */
        handleSearch(params) {
            console.log(params, '从查询页面传递过来的数据');
            this.columns = params.modelNameList;

            this.queryParams = {
                ...this.queryParams,
                ...params,
                pageNum: 1,
            };

            this.result = {};
            console.log(this.queryParams, '表单提交数据');
            this.getList();
        },
        /** 处理重置操作 */
        handleReset() {
            this.queryParams = {
                pageNum: 1,
                pageSize: 10,
                serialNumber: undefined,
                identityList: [],
                beginTime: undefined,
                endTime: undefined,
            };
            this.result = {};
            this.clearChart();
            // this.getList();
            this.showTable = false;
        },
        DrawChart() {
            this.loading = true;

            this.chart_x = this.dataList.map((item) => Object.keys(item)[0]);

            // 提取每个标识符对应的数据
            this.queryParams.identityList.forEach((identity, index) => {
                const values = [];
                this.dataList.forEach((item) => {
                    const time = Object.keys(item)[0];
                    const sensorArray = item[time];
                    const sensor = sensorArray.find((sensor) => sensor[identity] !== undefined);
                    if (sensor) {
                        values.push(sensor[identity]);
                    }
                });
                this.result[this.columns[index]] = values;
            });

            // 将 result 转换为数组格式
            this.tableData = this.dataList.map((item, index) => {
                const row = {};
                this.columns.forEach((column, colIndex) => {
                    row[column] = this.result[column][index];
                });
                return row;
            });

            this.showTable = false;
            this.$nextTick(() => {
                this.showTable = true;
            });
            console.log(this.result, 'result');
            if (this.chart) {
                // 动态生成 series 数据
                const series = Object.keys(this.result).map((identity, index) => ({
                    name: identity,
                    type: 'line',
                    stack: 'Total',
                    data: this.result[identity],
                }));

                this.chart.setOption({
                    title: {
                        text: '',
                    },
                    tooltip: {
                        trigger: 'axis',
                    },
                    legend: {
                        data: Object.keys(this.result),
                    },
                    toolbox: {
                        feature: {
                            saveAsImage: {},
                        },
                    },
                    dataZoom: [
                        {
                            type: 'slider',
                            xAxisIndex: 0,
                            start: 30,
                            end: 70,
                            backgroundColor: '#eee',
                            fillerColor: 'rgba(100, 149, 237, 0.2)',
                        },
                        {
                            type: 'inside',
                            xAxisIndex: 0,
                        },
                    ],
                    xAxis: {
                        type: 'category',
                        boundaryGap: false,
                        data: this.chart_x,
                    },
                    yAxis: {
                        type: 'value',
                        axisLine: { show: false },
                        axisTick: { show: false },
                        splitLine: { show: false },
                    },
                    series: series, // 替换为动态生成的 series 数据
                });
            }
            this.loading = false;
        },
        clearChart() {
            if (this.chart) {
                this.chart.clear(); // 清空图表内容但保留容器
                // 或者使用空配置
                this.chart.setOption({}, true); // 第二个参数 true 表示不合并配置
            }
        },
    },
};
</script>

<style lang="scss" scoped>
/* 全局样式或scoped样式 */
.no-divider-table {
    /* 移除表格外边框 */
    border: none;
}
.no-divider-table::before {
    /* 移除表格底部横线 */
    height: 0 !important;
}
::v-deep .no-divider-table td,
::v-deep .no-divider-table th {
    border: none !important;
}
.no-divider-table .el-table__header th {
    /* 移除表头边框 */
    border: none !important;
}
.no-divider-table .el-table__body tr:hover td {
    /* 可选：移除hover时的背景色变化 */
    background-color: transparent !important;
}
.no-divider-table .cell {
    border: none !important;
}
</style>
