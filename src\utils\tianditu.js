/**
 * 加载天地图API
 */
let loadRetryCount = 0;
const MAX_RETRIES = 3;

export function loadTMap() {
    return new Promise(function (resolve, reject) {
        if (typeof T !== 'undefined') {
            console.log('天地图API已加载，直接使用');
            resolve(T);
            return true;
        }

        // 设置加载超时处理
        let timeoutId = setTimeout(() => {
            console.error('天地图API加载超时');
            retryOrReject(reject, '加载超时');
        }, 10000);

        // 加载回调
        window.onTMapCallback = function () {
            clearTimeout(timeoutId);
            console.log('天地图API加载成功');
            loadRetryCount = 0; // 重置重试计数
            resolve(T);
        };

        // 使用https协议需要添加安全策略标签
        var protocolStr = document.location.protocol;
        if (protocolStr == 'https:') {
            let meta = document.createElement('meta');
            meta.httpEquiv = 'Content-Security-Policy';
            meta.content = 'upgrade-insecure-requests';
            meta.onerror = (e) => {
                console.warn('添加CSP标签失败:', e);
                // 继续加载，不视为致命错误
            };
            document.head.appendChild(meta);
        }

        // 获取API Key
        const apiKey = process.env.VUE_APP_TIANDITU_KEY;
        if (!apiKey) {
            console.error('未配置天地图API密钥，请在环境变量中设置VUE_APP_TIANDITU_KEY');
            reject(new Error('缺少天地图API密钥'));
            return;
        }
        console.log('使用天地图API Key:', apiKey);

        // 引入天地图
        let script = document.createElement('script');
        script.type = 'text/javascript';
        script.src = `https://api.tianditu.gov.cn/api?v=4.0&tk=${apiKey}&callback=onTMapCallback`;
        script.onerror = function (e) {
            clearTimeout(timeoutId);
            console.error('天地图API加载失败', e);
            retryOrReject(reject, e);
        };
        document.head.appendChild(script);
    });
}

/**
 * 重试加载或者拒绝Promise
 */
function retryOrReject(reject, error) {
    loadRetryCount++;
    if (loadRetryCount <= MAX_RETRIES) {
        console.log(`天地图API加载失败，正在进行第${loadRetryCount}次重试...`);

        // 移除之前失败的script标签
        const oldScript = document.querySelector('script[src*="api.tianditu.gov.cn"]');
        if (oldScript && oldScript.parentNode) {
            oldScript.parentNode.removeChild(oldScript);
        }

        // 延迟重试
        setTimeout(() => {
            loadTMap().then(
                (T) => window.onTMapCallback(T),
                (e) => reject(e)
            );
        }, 2000);
    } else {
        console.error(`天地图API加载失败，已重试${MAX_RETRIES}次`);
        reject(error);
    }
}

/**
 * 提供降级的瓦片URL处理函数
 * 当天地图API某个服务器响应慢时切换到其他服务器
 */
export function getAlternativeTileUrl(errorUrl) {
    if (!errorUrl || typeof errorUrl !== 'string') return null;

    // 提取服务器编号和其他URL部分
    const match = errorUrl.match(/t(\d)\.tianditu\.gov\.cn(.*)/);
    if (!match) return null;

    const serverNum = parseInt(match[1]);
    const urlPath = match[2];

    // 计算下一个要使用的服务器编号 (0-7)
    const nextServer = (serverNum + 1) % 8;

    // 返回替代URL
    return `https://t${nextServer}.tianditu.gov.cn${urlPath}`;
}
