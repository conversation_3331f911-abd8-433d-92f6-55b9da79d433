<template>
  <div>
    <el-card style="margin-bottom: 6px">
      <el-form :model="queryParams" ref="queryForm" :inline="true" label-width="75px" style="margin-bottom: -20px">
        <template v-if="localShowSearch">
          <el-form-item label="设备名称" prop="deviceName">
            <el-input v-model="queryParams.deviceName" placeholder="请输入设备名称" clearable size="small"
              @keyup.enter.native="handleQuery" style="width: 150px" />
          </el-form-item>
          <el-form-item label="设备编号" prop="serialNumber">
            <el-input v-model="queryParams.serialNumber" placeholder="请输入设备编号" clearable size="small"
              @keyup.enter.native="handleQuery" style="width: 150px" />
          </el-form-item>
          <el-form-item label="设备状态" prop="status">
            <el-select v-model="queryParams.status" placeholder="请选择设备状态" clearable size="small" style="width: 150px">
              <el-option :value="1" label="未激活" />
              <el-option :value="2" label="禁用" />
              <el-option :value="3" label="在线" />
              <el-option :value="4" label="离线" />
            </el-select>
          </el-form-item>
          <el-form-item label="我的分组">
            <el-select v-model="queryParams.groupId" placeholder="请选择我的分组" clearable size="small" style="width: 150px">
              <el-option v-for="group in myGroupList" :key="group.groupId" :label="group.groupName"
                :value="group.groupId" />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
            <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
          </el-form-item>
        </template>

        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <!-- <el-dropdown @command="(command) => handleCommand(command)" v-hasPermi="['iot:device:add']">
              <el-button size="mini" type="primary" plain>
                新增设备
                <i class="el-icon-arrow-down el-icon--right"></i>
              </el-button>
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item command="handleEditDevice" v-hasPermi="['iot:device:add']">手动添加</el-dropdown-item>
                <el-dropdown-item command="handleBatchImport" v-hasPermi="['iot:device:add']">批量导入</el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown> -->
            <el-col :span="1.5">
              <el-dropdown @command="(command) => handleCommand(command)" v-hasPermi="['iot:device:assignment']">
                <el-button size="mini" type="primary" plain>
                  分配设备
                  <i class="el-icon-arrow-down el-icon--right"></i>
                </el-button>
                <el-dropdown-menu slot="dropdown">
                  <el-dropdown-item command="handleSelectAllot"
                    v-hasPermi="['iot:device:assignment']">选择分配</el-dropdown-item>
                  <el-dropdown-item command="handleImportAllot"
                    v-hasPermi="['iot:device:assignment']">导入分配</el-dropdown-item>
                </el-dropdown-menu>
              </el-dropdown>
            </el-col>
          </el-col>
          <el-col :span="1.5">
            <el-button type="primary" plain icon="el-icon-s-grid" size="mini"
              @click="handleChangeShowType">切换展示</el-button>
          </el-col>
          <el-col :span="1.5" v-if="showType === 'list'">
            <el-button type="success" plain icon="el-icon-download" size="mini" @click="handleExport">导出设备</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="danger" plain icon="el-icon-delete" size="mini" @click="handleBatchDelete"
              v-hasPermi="['iot:device:remove']">批量删除</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-checkbox v-model="queryParams.showChild" style="margin: 5px 0 0"
              @change="handleQuery">显示下级机构数据</el-checkbox>
            <el-tooltip content="选中后，本级可以看下级的数据" placement="top"><i class="el-icon-question"></i></el-tooltip>
          </el-col>
          <right-toolbar :showSearch.sync="localShowSearch" @queryTable="queryTable"></right-toolbar>
        </el-row>
      </el-form>
    </el-card>
  </div>
</template>

<script>
import RightToolbar from "@/components/RightToolbar";

export default {
  name: "ToolForm",
  components: {
    RightToolbar
  },
  props: {
    // 父组件传入的查询参数
    queryParamsProp: {
      type: Object,
      required: true
    },
    // 分组列表
    myGroupList: {
      type: Array,
      default: () => []
    },
    // 是否显示搜索条件
    showSearch: {
      type: Boolean,
      default: true
    },
    // 展示类型
    showType: {
      type: String,
      default: 'list'
    }
  },
  data() {
    return {
      // 本地查询参数（从父组件同步）
      queryParams: this.queryParamsProp,
      localShowSearch: this.showSearch,
    };
  },
  watch: {
    // 监听父组件传入的查询参数变化
    queryParamsProp: {
      handler(val) {
        this.queryParams = val;
      },
      deep: true
    },
    // 监听父组件传入的showSearch变化
    showSearch: {
      handler(val) {
        this.localShowSearch = val;
      }
    },
    // 监听本地showSearch变化并同步到父组件
    localShowSearch: {
      handler(val) {
        this.$emit('update:showSearch', val);
      }
    }
  },
  methods: {
    /** 搜索按钮操作 */
    handleQuery() {
      this.$emit('search');
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.queryParams.productId = null;
      this.queryParams.groupId = null;
      this.queryParams.serialNumber = null;
      this.$refs.queryForm.resetFields();
      this.$emit('reset');
    },
    /** 切换显示方式 */
    handleChangeShowType() {
      this.$emit('changeShowType');
    },
    // 查询表格数据
    queryTable() {
      this.$emit('queryTable');
    },
    // 新增设备操作触发
    handleCommand(command) {
      this.$emit('command', command);
    },
    // 导出设备操作
    handleExport() {
      this.$emit('export');
    },
    // 批量删除操作
    handleBatchDelete() {
      this.$emit('batchDelete');
    }
  }
};
</script>

<style scoped>
.el-dropdown-menu__item {
  font-size: 12px;
}

.mb8 {
  margin-bottom: 8px;
}
</style>
