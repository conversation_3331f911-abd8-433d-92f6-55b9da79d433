<template>
    <div id="recordDetail">
        <el-container>
            <div style="width: 220px">
                <div class="record-list-box-box">
                    <div style="margin: 22px 20px 10px">
                        <el-date-picker
                            size="mini"
                            style="width: 200px"
                            v-model="chooseDate"
                            :picker-options="pickerOptions"
                            type="date"
                            value-format="yyyy-MM-dd"
                            placeholder="请选择日期"
                            @change="dateChange()"
                        ></el-date-picker>
                    </div>
                    <div class="record-list-box" :style="recordListStyle">
                        <ul v-if="detailFiles.length > 0" class="record-list" v-infinite-scroll="infiniteScroll">
                            <li v-for="(item, index) in detailFiles" :key="index" class="record-list-item">
                                <el-tag v-if="choosedFile != item" @click="chooseFile(item)">
                                    <i class="el-icon-video-camera"></i>
                                    {{ item.substring(0, 17) }}
                                </el-tag>
                                <el-tag type="danger" v-if="choosedFile == item">
                                    <i class="el-icon-video-camera"></i>
                                    {{ item.substring(0, 17) }}
                                </el-tag>
                                <a
                                    class="el-icon-download"
                                    style="color: #409eff; font-weight: 600; margin-left: 10px"
                                    :href="`${getFileBasePath()}/download.html?url=download/${recordFile.app}/${recordFile.stream}/${chooseDate}/${item}`"
                                    target="_blank"
                                />
                            </li>
                        </ul>
                        <div v-if="detailFiles.length == 0" class="record-list-no-val">暂无数据</div>
                    </div>
                </div>
            </div>
            <el-main style="padding: 22px">
                <div class="playBox" :style="playerStyle">
                    <player ref="recordVideoPlayer" :videoUrl="videoUrl" :height="true"></player>
                </div>
            </el-main>
        </el-container>
    </div>
</template>

<script>
import moment from 'moment';
import { getServerRecordByDate, getServerRecordByFile } from '@/api/iot/record';
import player from '@/views/components/player/easyplayer.vue';
export default {
    name: 'ReocrdDetail',
    components: {
        player,
    },
    props: ['recordFile', 'mediaServerId', 'recordApi', 'dateFiles'],
    data() {
        return {
            recordListStyle: {
                height: this.winHeight + 'px',
                margin: '10px 20px',
            },
            playerStyle: {
                margin: 'auto',
                'margin-bottom': '0px',
                height: this.winHeight + 'px',
            },
            winHeight: window.innerHeight - 220,
            dateFilesObj: [],
            detailFiles: [],
            chooseDate: '',
            videoUrl: null,
            choosedFile: null,
            queryDate: new Date(),
            currentPage: 1,
            count: 1000000,
            total: 0,
            direction: 'ltr',
            drawer: false,
            showTaskBox: false,
            taskTimeRange: [],
            taskListEnded: [],
            taskListForRuning: [],
            sliderMIn: 0,
            sliderMax: 86400,
            autoPlay: true,
            taskUpdate: null,
            tabVal: 'running',
            timeFormat: '00:00:00',
            playTime: 0,
            playinfo: {},
            pickerOptions: {
                cellClassName: (date) => {
                    // 通过显示一个点标识这一天有录像
                    let time = moment(date).format('YYYY-MM-DD');
                    if (this.dateFilesObj[time]) {
                        return 'data-picker-true';
                    } else {
                        return 'data-picker-false';
                    }
                },
            },
        };
    },
    computed: {},
    mounted() {
        this.recordListStyle.height = this.winHeight + 'px';
        this.playerStyle['height'] = this.winHeight + 'px';
        this.getDateInYear(() => {
            if (Object.values(this.dateFilesObj).length > 0) {
                this.chooseDate = Object.values(this.dateFilesObj)[Object.values(this.dateFilesObj).length - 1];
                this.dateChange();
            }
        });
    },
    destroyed() {},
    methods: {
        dateChange() {
            this.playTime = 0;
            this.detailFiles = [];
            this.currentPage = 1;
            this.sliderMIn = 0;
            this.sliderMax = 86400;
            let chooseFullDate = new Date(this.chooseDate + ' ' + this.timeFormat);
            if (chooseFullDate.getFullYear() !== this.queryDate.getFullYear() || chooseFullDate.getMonth() !== this.queryDate.getMonth()) {
            }
            this.queryRecordDetails(() => {
                if (this.detailFiles.length > 0) {
                    let timeForFile = this.getTimeForFile(this.detailFiles[0]);
                    let lastTimeForFile = this.getTimeForFile(this.detailFiles[this.detailFiles.length - 1]);
                    let timeNum = timeForFile[0].getTime() - new Date(this.chooseDate + ' ' + this.timeFormat).getTime();
                    let lastTimeNum = lastTimeForFile[1].getTime() - new Date(this.chooseDate + ' ' + this.timeFormat).getTime();
                    this.playTime = parseInt(timeNum / 1000);
                    this.sliderMIn = parseInt(timeNum / 1000 - ((timeNum / 1000) % (60 * 60)));
                    this.sliderMax = parseInt(lastTimeNum / 1000 - ((lastTimeNum / 1000) % (60 * 60))) + 60 * 60;
                }
            });
        },
        infiniteScroll() {
            if (this.total > this.detailFiles.length) {
                this.currentPage++;
                this.queryRecordDetails();
            }
        },
        queryRecordDetails: function () {
            const query = {
                recordApi: this.recordApi,
                app: this.recordFile.app,
                stream: this.recordFile.stream,
                startTime: this.chooseDate + ' 00:00:00',
                endTime: this.chooseDate + ' 23:59:59',
                pageNum: this.currentPage,
                pageSize: this.count,
            };
            getServerRecordByFile(query).then((res) => {
                console.log(res);
                if (res.code === 200) {
                    this.total = res.data.total;
                    this.detailFiles = this.detailFiles.concat(res.data.list);
                }
            });
        },
        chooseFile(file) {
            this.choosedFile = file;
            if (file == null) {
                this.videoUrl = '';
            } else {
                this.videoUrl = `${this.getFileBasePath()}/download/${this.recordFile.app}/${this.recordFile.stream}/${this.chooseDate}/${this.choosedFile}`;
                console.log(this.videoUrl);
            }
        },

        getFileBasePath() {
            return this.recordApi;
        },
        getTimeForFile(file) {
            let timeStr = file.substring(0, 17);
            if (timeStr.indexOf('~') > 0) {
                timeStr = timeStr.replaceAll('-', ':');
            }
            let timeArr = timeStr.split('-');
            let starTime = new Date(this.chooseDate + ' ' + timeArr[0]);
            let endTime = new Date(this.chooseDate + ' ' + timeArr[1]);
            if (this.checkIsOver24h(starTime, endTime)) {
                endTime = new Date(this.chooseDate + ' ' + '23:59:59');
            }
            return [starTime, endTime, endTime.getTime() - starTime.getTime()];
        },

        checkIsOver24h(starTime, endTime) {
            return starTime > endTime;
        },

        getDateInYear() {
            const query = {
                recordApi: this.recordApi,
                app: this.recordFile.app,
                stream: this.recordFile.stream,
            };
            getServerRecordByDate(query).then((res) => {
                if (res.code === 200) {
                    if (res.data.length > 0) {
                        for (let i = 0; i < res.data.length; i++) {
                            this.dateFilesObj[res.data[i]] = res.data[i];
                        }
                        console.log(this.dateFilesObj);
                    }
                }
            });
        },
        goBack() {
            this.$router.push('/record');
        },
    },
};
</script>

<style>
.record-list-box-box {
    width: 200px;
    align-items: center;
}

.record-list-box {
    width: 200px;
    list-style: none;
    border: 1px solid #d2dae1;
    margin: 10px 20px;
    overflow: auto;
    background-color: #fff;
}

.record-list {
    list-style: none;
    padding: 0;
    margin: 0;
    background-color: #fff;
}

.record-list-no-val {
    position: absolute;
    color: #9f9f9f;
    top: 200px;
    left: 90px;
}

.record-list-item {
    padding: 0;
    margin: 10px 10px;
    cursor: pointer;
}
</style>
