<template>
    <div style="padding: 6px">
        <!-- 查询表单卡片 -->
        <el-card v-show="showSearch" style="margin-bottom: 5px">
            <search-form ref="searchForm" :initQuery="queryParams" @search="handleSearch" @reset="handleReset" />
        </el-card>

        <!-- 数据表格卡片 -->
        <el-card style="padding-bottom: 100px">
            <el-table v-loading="loading" :data="dataList" border>
                <el-table-column label="设备编号" align="center" prop="serialNumber" />
                <el-table-column label="标识符" align="center" prop="identity" />
                <el-table-column label="类型" align="center" prop="logType">
                    <template slot-scope="scope">
                        <span v-if="scope.row.logType === 1">属性上报</span>
                        <span v-if="scope.row.logType === 2">事件上报</span>
                        <span v-if="scope.row.logType === 3">调用功能</span>
                        <span v-if="scope.row.logType === 4">设备升级</span>
                        <span v-if="scope.row.logType === 5">设备上线</span>
                        <span v-if="scope.row.logType === 6">设备离线</span>
                    </template>
                </el-table-column>
                <el-table-column label="模式" align="center" prop="mode">
                    <template slot-scope="scope">
                        <span v-if="scope.row.mode === 1">影子模式</span>
                        <span v-if="scope.row.mode === 2">在线模式</span>
                        <span v-if="scope.row.mode === 3">其他</span>
                    </template>
                </el-table-column>
                <el-table-column label="日志值" align="center" prop="logValue" />
                <el-table-column label="备注" align="center" prop="remark" />
                <el-table-column label="创建时间" align="center" prop="createTime" width="180" />
            </el-table>

            <!-- 分页组件 -->
            <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize" @pagination="getList" />
        </el-card>
    </div>
</template>

<script>
import { listDeviceLog } from '@/api/iot/deviceLog';
import SearchForm from '@/views/dataManager/components/searchForm';

export default {
    name: 'DeviceHistoryData',
    components: {
        SearchForm,
    },
    data() {
        return {
            // 遮罩层
            loading: false,
            // 总条数
            total: 0,
            // 历史数据表格数据
            dataList: [],
            // 显示搜索条件
            showSearch: true,
            // 查询参数
            queryParams: {
                pageNum: 1,
                pageSize: 10,
                serialNumber: undefined,
                identityList: [],
                beginTime: undefined,
                endTime: undefined,
            },
        };
    },
    created() {
        // this.getList();
    },
    methods: {
        /** 查询历史数据列表 */
        getList() {
            this.loading = true;

            listDeviceLog(this.queryParams).then((response) => {
                this.dataList = response.rows;
                this.total = response.total;
                this.loading = false;
            });
        },
        /** 处理搜索表单提交 */
        handleSearch(params) {
            this.queryParams = {
                ...this.queryParams,
                ...params,
                pageNum: 1,
            };
            console.log(this.queryParams, '表单提交数据');
            this.getList();
        },
        /** 处理重置操作 */
        handleReset() {
            this.queryParams = {
                pageNum: 1,
                pageSize: 10,
                serialNumber: undefined,
                identityList: [],
                beginTime: undefined,
                endTime: undefined,
            };
            this.dataList = [];
            this.total = 0;
            this.loading = false;
            // this.getList();
        },
    },
};
</script>
