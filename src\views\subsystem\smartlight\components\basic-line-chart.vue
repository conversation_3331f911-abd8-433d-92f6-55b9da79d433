<template>
  <div ref="basicLineChartContainer" class="basic-line-chart"></div>
</template>

<script>
import * as echarts from 'echarts';

export default {
  name: 'EchartsComponent',
  data() {
    return {
      chartInstance: null,
    };
  },
  mounted() {
    this.initEcharts();
  },
  beforeD<PERSON>roy() {
    if (this.chartInstance) {
      this.chartInstance.dispose();
      this.chartInstance = null;
    }
  },
  methods: {
    initEcharts() {
      const options = {
        backgroundColor: '#FFF',
        grid: {
          top: '5%',
          bottom: '5%',
          left: '5%',
          right: '2%'
        },
        tooltip: {
          trigger: 'axis',
          label: {
            show: true
          },
        },
        xAxis: {
          boundaryGap: true, // 默认，坐标轴留白策略
          axisLine: {
            show: false
          },
          splitLine: {
            show: false
          },
          axisTick: {
            show: false,
            alignWithLabel: true
          },
          data: [
            '1月', '2月', '3月', '4月', '5月', '6月',
            '7月', '8月', '9月', '10月', '11月', '12月',
          ]
        },
        yAxis: {
          axisLine: {
            show: false
          },
          splitLine: {
            show: true,
            lineStyle: {
              type: 'dashed',
              color: '#E5E5E5'
            }
          },
          axisTick: {
            show: false
          },
          splitArea: {
            show: true,
            areaStyle: {
              color: '#fff'
            }
          }
        },
        series: [{
          showSymbol: false,
          smooth: true, // 开启平滑处理
          type: 'line',
          lineStyle: {
            color: '#17a6fa',
            shadowBlur: 12,   // 模糊程度
            shadowColor: 'rgba(0, 0, 0, 0.12)',  // 阴影颜色
            shadowOffsetX: 0,
            shadowOffsetY: 4,
            width: 4,
          },
          itemStyle: {
            color: 'rgb(33,148,246)',
            borderWidth: 1,
            borderColor: '#FFF'
          },
          areaStyle: {
            normal: {
              color: new echarts.graphic.LinearGradient(
                0, 0, 0, 1,
                [
                  { offset: 0, color: 'rgba(0, 136, 212, 0.2)' },
                  { offset: 1, color: 'rgba(0, 136, 212, 0)' }
                ],
                false
              ),
              // shadowColor: 'rgba(0, 0, 0, 0.1)',
              // shadowBlur: 10
            }
          },
          label: {
            show: false,
            distance: 1,
            emphasis: {
              show: true,
              offset: [25, -2],
              color: '#FFF',
              padding: [8, 20, 8, 6],
              // formatter: function(params) {
              //     var name = params.name;
              //     var value = params.data;
              //     var str = name + '\n数据量：' + value;
              //     return str;
              // },
              // rich: {
              //     bg: {
              //         backgroundColor: {
              //             image: this.uploadedDataURL
              //         },
              //         width: 78,
              //         color: '#FFF',
              //         padding: [20, 0, 20, 10]
              //     },
              //     br: {
              //         width: '100%',
              //         height: '100%'
              //     }
              // }
            }
          },
          data: [
            2000, 1800, 2800, 900, 1600, 2000,
            3000, 2030, 1356, 1900, 4000, 3000,
          ]
        }]
      };

      this.chartInstance = echarts.init(this.$refs.basicLineChartContainer);
      this.chartInstance.setOption(options);
    }
  }
};
</script>

<style scoped>
  .basic-line-chart {
    width: 100%;
    height: 100%;
    position: relative;
  }
</style>