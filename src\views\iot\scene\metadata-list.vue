<template>
    <el-dialog :close-on-click-modal="false" title="选择元数据" :visible.sync="open" width="800px">
        <div style="margin-top: -55px">
            <el-divider style="margin-top: -30px"></el-divider>
            <el-form :model="queryParams" ref="queryForm" :inline="true" label-width="68px">
                <el-form-item label="元数据名称" prop="name">
                    <el-input v-model="queryParams.name" placeholder="请输入元数据名称" clearable size="small"
                        @keyup.enter.native="handleQuery" />
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
                    <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
                </el-form-item>
            </el-form>

            <el-table v-loading="loading" ref="singleTable" :data="metadataList" @row-click="rowClick"
                highlight-current-row size="mini">
                <el-table-column label="选择" width="50" align="center">
                    <template slot-scope="scope">
                        <input type="radio" :checked="scope.row.isSelect" name="metadata" />
                    </template>
                </el-table-column>
              <el-table-column label="ID" align="center" prop="id" />
              <el-table-column label="名称" align="center" prop="name" />
              <el-table-column label="类型" align="center" prop="type">
                <template slot-scope="scope">
                  <dict-tag :options="dict.type.rule_script_action" :value="scope.row.type"
                            size="small" />
                </template>
              </el-table-column>
              <el-table-column label="数据json" align="center" prop="dataJson" />
              <el-table-column label="是否生效" align="center" prop="enable">
                <template slot-scope="scope">
                  <dict-tag :options="dict.type.iot_is_enable" :value="scope.row.enable"
                            size="small" />
                </template>
              </el-table-column>
              <el-table-column label="备注" align="center" prop="remark" />
            </el-table>

            <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum"
                :limit.sync="queryParams.pageSize" @pagination="getList" />
        </div>
        <div slot="footer" class="dialog-footer">
            <el-button @click="confirmSelectMeta" type="primary">确定</el-button>
            <el-button @click="closeDialog" type="info">关 闭</el-button>
        </div>
    </el-dialog>
</template>

<script>
import { listMetadata} from "@/api/iot/metadata";

export default {
    name: 'MetadataList',
    dicts: [ 'rule_script_action', 'iot_is_enable'],
    data() {
        return {
            // 遮罩层
            loading: true,
            // 总条数
            total: 0,
            // 打开选择产品对话框
            open: false,
            // 产品列表
            metadataList: [],
            // 选中的产品编号
            selectMetaId: 0,
            // 选中的产品
            metadata: {},
            // 查询参数
            queryParams: {
                pageNum: 1,
                pageSize: 10,
                name: '',
                type:null
            },
        };
    },
    created() { },
    methods: {
        /** 查询产品列表 */
        getList() {
            this.loading = true;
          listMetadata(this.queryParams).then((response) => {
                //产品列表初始化isSelect值，用于单选
                for (let i = 0; i < response.rows.length; i++) {
                    response.rows[i].isSelect = false;
                }
                this.metadataList = response.rows;
                this.total = response.total;
                this.loading = false;
                // 设置产品选中
                this.setRadioSelected(this.selectMetaId);
            });
        },
        /** 搜索按钮操作 */
        handleQuery() {
            this.queryParams.pageNum = 1;
            this.getList();
        },
        /** 重置按钮操作 */
        resetQuery() {
            this.resetForm('queryForm');
            this.handleQuery();
        },
        /** 单选数据 */
        rowClick(metadata) {
            if (metadata != null) {
                this.setRadioSelected(metadata.id);
                this.metadata = metadata;
            }
        },
        /** 设置单选按钮选中 */
        setRadioSelected(id) {
            for (let i = 0; i < this.metadataList.length; i++) {
                if (this.metadataList[i].id == id) {
                    this.metadataList[i].isSelect = true;
                } else {
                    this.metadataList[i].isSelect = false;
                }
            }
        },
        /**确定选择，产品传递给父组件 */
        confirmSelectMeta() {
          console.log("this.metadata"+JSON.stringify(this.metadata))
            this.$emit('metadataEvent', this.metadata);
            this.open = false;
        },
        /**关闭对话框 */
        closeDialog() {
            this.open = false;
        },
    },
};
</script>
