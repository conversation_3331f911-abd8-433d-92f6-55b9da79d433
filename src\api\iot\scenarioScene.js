import request from '@/utils/request'

// 查询场景设备联动列表
export function listScene(query) {
  return request({
    url: '/iot/scenarioScene/list',
    method: 'get',
    params: query
  })
}

// 查询场景设备联动详细
export function getScene(id) {
  return request({
    url: '/iot/scenarioScene/' + id,
    method: 'get'
  })
}

// 新增场景设备联动
export function addScene(data) {
  return request({
    url: '/iot/scenarioScene',
    method: 'post',
    data: data
  })
}

// 修改场景设备联动
export function updateScene(data) {
  return request({
    url: '/iot/scenarioScene',
    method: 'put',
    data: data
  })
}

// 删除场景设备联动
export function delScene(id) {
  return request({
    url: '/iot/scenarioScene/' + id,
    method: 'delete'
  })
}
