<style scoped>
.data_monitoring_main {
    width: 20vw;
    height: 24.4vh;
}
</style>

<template>
    <div class="data_monitoring_main">
        <div ref="dataMonitoringChart" style="width: 20vw; height: 24.4vh"></div>
    </div>
</template>

<script>
export default {
    components: {},
    created() {
        this.init();
    },
    methods: {
        init() {
            this.getServer();
        },
        /** 查询服务器信息 */
        getServer() {
            this.$nextTick(() => {
                this.drawPieCpu();
            });
        },
        drawPieCpu() {
            // 基于准备好的dom，初始化echarts实例
            let myChart = this.$echarts.init(this.$refs.dataMonitoringChart);
            var option;

            option = {
                tooltip: {
                    trigger: 'axis',
                    axisPointer: {
                        type: 'shadow',
                    },
                },
                legend: {
                    data: ['当前数量', '累计总数'], // 对应series的name
                    textStyle: {
                        // 图例文字样式
                        color: '#fff', // 字体颜色
                    },
                },
                grid: {
                    left: '3%',
                    right: '4%',
                    bottom: '3%',
                    containLabel: true,
                },
                xAxis: {
                    type: 'value',
                    boundaryGap: [0, 0.01],
                },
                yAxis: {
                    type: 'category',
                    data: ['连接数量', '会话数量', '订阅数量', '路由数量', '保留消息'],
                    axisLabel: {
                        // 这是控制y轴标签样式的关键配置
                        color: '#fff', // 字体颜色
                        // fontSize: 12, // 字体大小
                        // fontFamily: 'Arial', // 字体类型
                        // fontWeight: 'bold', // 字体粗细
                        // fontStyle: 'italic', // 字体样式
                        // padding: [0, 0, 0, 10], // 标签与轴线距离
                    },
                },
                series: [
                    {
                        name: '当前数量',
                        type: 'bar',
                        data: [18203, 13489, 19034, 104970, 330230],
                        label: {
                            color: '#fff', // 字体颜色
                        },
                    },
                    {
                        name: '累计总数',
                        type: 'bar',
                        data: [19325, 23438, 31000, 121594, 681807],
                    },
                ],
            };

            option && myChart.setOption(option);
        },
    },
};
</script>
