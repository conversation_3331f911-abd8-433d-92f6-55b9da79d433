<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="标识符" prop="identity">
        <el-input v-model="queryParams.identity" placeholder="请输入标识符" clearable size="small"
          @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="时间范围" prop="timeRange">
        <el-date-picker v-model="dateRange" size="small" style="width: 340px" value-format="yyyy-MM-dd HH:mm:ss"
          type="datetimerange" range-separator="-" start-placeholder="开始日期" end-placeholder="结束日期"></el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">查询</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-table v-loading="loading" :data="dataList" border>
      <el-table-column label="设备编号" align="center" prop="serialNumber" />
      <el-table-column label="标识符" align="center" prop="identity" />
      <el-table-column label="类型" align="center" prop="logType">
        <template slot-scope="scope">
          <span v-if="scope.row.logType === 1">属性上报</span>
          <span v-if="scope.row.logType === 2">事件上报</span>
          <span v-if="scope.row.logType === 3">调用功能</span>
          <span v-if="scope.row.logType === 4">设备升级</span>
          <span v-if="scope.row.logType === 5">设备上线</span>
          <span v-if="scope.row.logType === 6">设备离线</span>
        </template>
      </el-table-column>
      <el-table-column label="模式" align="center" prop="logType">
        <template slot-scope="scope">
          <span v-if="scope.row.logType === 1">影子模式</span>
          <span v-if="scope.row.logType === 2">在线模式</span>
          <span v-if="scope.row.logType === 3">其他</span>
        </template>
      </el-table-column>
      <el-table-column label="日志值" align="center" prop="logValue" />
      <el-table-column label="备注" align="center" prop="remark" />
      <el-table-column label="创建时间" align="center" prop="createTime" width="180" />
    </el-table>

    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize"
      @pagination="getList" />
  </div>
</template>

<script>
import { listDeviceLog } from '@/api/iot/deviceLog'

export default {
  name: "DeviceHistory",
  props: {
    device: {
      type: Object,
      required: true
    }
  },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 历史数据表格数据
      dataList: [],
      // 日期范围
      dateRange: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        serialNumber: undefined,
        identity: undefined,
        beginTime: undefined,
        endTime: undefined
      }
    };
  },
  watch: {
    device: {
      handler(val) {
        if (val && val.serialNumber) {
          this.queryParams.serialNumber = val.serialNumber;
          this.getList();
        }
      },
      immediate: true
    }
  },
  methods: {
    /** 查询历史数据列表 */
    getList() {
      this.loading = true;

      // 处理日期范围
      if (this.dateRange && this.dateRange.length > 0) {
        this.queryParams.beginTime = this.dateRange[0];
        this.queryParams.endTime = this.dateRange[1];
      } else {
        this.queryParams.beginTime = undefined;
        this.queryParams.endTime = undefined;
      }

      listDeviceLog(this.queryParams).then(response => {
        this.dataList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dateRange = [];
      this.resetForm("queryForm");
      this.queryParams.serialNumber = this.device.serialNumber;
      this.handleQuery();
    },
  }
};
</script>
