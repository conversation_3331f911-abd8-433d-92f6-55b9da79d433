<template>
  <div style="height: calc(100vh - 50px); padding: 16px; background-color: #ECF3F8;">
    <div style="width: 100%; height: 100%; display: flex; flex-direction: column;">
      <el-row :gutter="16" style="flex: 0 0 18%;">
        <el-col :span="6">
          <div class="grid-content" style="padding: 16px;">
            <div style="width: 100%; height: 100%;">
              <div style="width: 100%; height: 75%;">
                <div style="height: 100%; width: 50%; float: left;">
                  <div style="width: 100%; height: 40%; display: flex; align-items: center;">
                    <span style="font-size: 0.84rem; color: #74788D;">今日用量</span>
                  </div>
                  <div style="width: 100%; height: 60%; display: flex; align-items: center;">
                    <span style="font-size: 1.2rem; color: #495057; font-weight: 600;">20&nbsp;<a style="font-size: 1rem;">kw/h</a></span>
                  </div>
                </div>
                <div style="height: 100%; width: 50%; float: right;">
                  <SimpleCurveDay/>
                </div>
              </div>
              <div style="width: 100%; height: 25%; display: flex; align-items: center;">
                <span style="font-size: 0.8rem; background-color: rgba(42, 181, 125, .25) !important; color: #2ab57d !important; padding: .2em .2em .1em .2em;">-25.9k</span>
                <!-- <span style="font-size: 0.78rem; background-color: rgba(253,98,94,.25)!important; color: #fd625e !important; padding: .2em .2em .1em .2em;">-20.9k</span> -->
                <span style="float: left; font-size: 0.84rem; color: #74788D; margin-left: 0.2rem;">昨日用量：56kw/h</span>
              </div>
            </div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="grid-content" style="padding: 16px;">
            <div style="width: 100%; height: 100%;">
              <div style="width: 100%; height: 75%;">
                <div style="height: 100%; width: 50%; float: left;">
                  <div style="width: 100%; height: 40%; display: flex; align-items: center;">
                    <span style="font-size: 0.84rem; color: #74788D;">本周用量</span>
                  </div>
                  <div style="width: 100%; height: 60%; display: flex; align-items: center;">
                    <span style="font-size: 1.2rem; color: #495057; font-weight: 600;">138&nbsp;<a style="font-size: 1rem;">kw/h</a></span>
                  </div>
                </div>
                <div style="height: 100%; width: 50%; float: right;">
                  <SimpleCurveWeek/>
                </div>
              </div>
              <div style="width: 100%; height: 25%; display: flex; align-items: center;">
                <span style="font-size: 0.8rem; background-color: rgba(253,98,94,.25)!important; color: #fd625e !important; padding: .2em .2em .1em .2em;">+20.9k</span>
                <span style="float: left; font-size: 0.84rem; color: #74788D; margin-left: 0.2rem;">上周用量：56kw/h</span>
              </div>
            </div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="grid-content" style="padding: 16px;">
            <div style="width: 100%; height: 100%;">
              <div style="width: 100%; height: 75%;">
                <div style="height: 100%; width: 50%; float: left;">
                  <div style="width: 100%; height: 40%; display: flex; align-items: center;">
                    <span style="font-size: 0.84rem; color: #74788D;">本月用量</span>
                  </div>
                  <div style="width: 100%; height: 60%; display: flex; align-items: center;">
                    <span style="font-size: 1.2rem; color: #495057; font-weight: 600;">967&nbsp;<a style="font-size: 1rem;">kw/h</a></span>
                  </div>
                </div>
                <div style="height: 100%; width: 50%; float: right;">
                  <SimpleCurveMoon/>
                </div>
              </div>
              <div style="width: 100%; height: 25%; display: flex; align-items: center;">
                <span style="font-size: 0.8rem; background-color: rgba(253,98,94,.25)!important; color: #fd625e !important; padding: .2em .2em .1em .2em;">+20.9k</span>
                <span style="float: left; font-size: 0.84rem; color: #74788D; margin-left: 0.2rem;">上月用量：56kw/h</span>
              </div>
            </div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="grid-content" style="padding: 16px;">
            <div style="width: 100%; height: 100%;">
              <div style="width: 100%; height: 75%;">
                <div style="height: 100%; width: 50%; float: left;">
                  <div style="width: 100%; height: 40%; display: flex; align-items: center;">
                    <span style="font-size: 0.84rem; color: #74788D;">本年用量</span>
                  </div>
                  <div style="width: 100%; height: 60%; display: flex; align-items: center;">
                    <span style="font-size: 1.2rem; color: #495057; font-weight: 600;">5635&nbsp;<a style="font-size: 1rem;">kw/h</a></span>
                  </div>
                </div>
                <div style="height: 100%; width: 50%; float: right;">
                  <SimpleCurveYear/>
                </div>
              </div>
              <div style="width: 100%; height: 25%; display: flex; align-items: center;">
                <span style="font-size: 0.8rem; background-color: rgba(253,98,94,.25)!important; color: #fd625e !important; padding: .2em .2em .1em .2em;">+20.9k</span>
                <span style="float: left; font-size: 0.84rem; color: #74788D; margin-left: 0.2rem;">去年用量：56kw/h</span>
              </div>
            </div>
          </div>
        </el-col>
      </el-row>
      <el-row :gutter="16" style="flex: 0 0 82%;">
        <el-col :span="18">
          <div class="grid-content" style="padding: 16px;">
            <div style="width: 100%; height: 100%; ">
              <div style="width: 100%; height: 8%; font-size: 16px; display: flex; align-items: center; justify-content: flex-end;">
                <el-dropdown>
                  <span class="el-dropdown-link">
                    选择时间<i class="el-icon-arrow-down el-icon--right"></i>
                  </span>
                  <el-dropdown-menu slot="dropdown">
                    <el-dropdown-item>今日</el-dropdown-item>
                    <el-dropdown-item>本周</el-dropdown-item>
                    <el-dropdown-item>本月</el-dropdown-item>
                    <el-dropdown-item>本年</el-dropdown-item>
                  </el-dropdown-menu>
                </el-dropdown>
              </div>
              <div style="width: 100%; height: 92%;">
                <BasicLineChart/>
              </div>
            </div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="grid-content" style="padding: 16px;">
            <div style="width: 100%; height: 100%; ">
              <div style="width: 100%; height: 50%; ">
                <simpleCurveCount/>
              </div>
              <div style="width: 100%; height: 50%;">
                <SimplePurpose/>
              </div>
            </div>
          </div>
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<script>
  import SimpleCurveDay from './components/simple-curve-day.vue';
  import SimpleCurveWeek from './components/simple-curve-week.vue';
  import SimpleCurveMoon from './components/simple-curve-moon.vue';
  import SimpleCurveYear from './components/simple-curve-year.vue';
  import simpleCurveCount from './components/simple-curve-count.vue';
  import SimplePurpose from './components/simple-purpose.vue';//照明用途
  import BasicLineChart from './components/basic-line-chart.vue';//详细信息
  export default {
    components: {
      SimpleCurveDay,
      SimpleCurveWeek,
      SimpleCurveMoon,
      SimpleCurveYear,
      simpleCurveCount,
      SimplePurpose,
      BasicLineChart
    },
  };
</script>

<style scoped>
  .el-row {
    display: flex;
    flex-wrap: wrap;
    /* margin-bottom: 16px; */
    padding-bottom: 16px;
  }

  .el-row:last-child {
    /* margin-bottom: 0; */
    padding-bottom: 0;
  }

  .grid-content {
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
    border: 1px solid #FFFFFF;
    min-height: 100%;
    color: #DCDFE6;
    cursor: pointer;
    background-color: #FFFFFF;
    border-radius:12px;
  }

  .grid-content:hover{
    border: 1px solid #409EFF;
  }

  .el-col {
    display: flex;
    flex-direction: column;
    align-items: stretch;
  }
</style>