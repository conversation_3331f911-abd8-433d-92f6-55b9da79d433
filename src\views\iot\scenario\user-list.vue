<template>
    <el-dialog :close-on-click-modal="false" title="选择用户" :visible.sync="open" width="800px" append-to-body>
        <el-form :model="queryParams" ref="queryForm" :inline="true" label-width="68px" style="margin-bottom: -10px">
            <el-form-item label="用户名称" prop="userName">
                <el-input v-model="queryParams.userName" placeholder="请输入用户名称" clearable size="small" @keyup.enter.native="handleQuery" />
            </el-form-item>
            <el-form-item>
                <el-button icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
                <el-button icon="el-icon-refresh" size="mini" @click="handleResetQuery">重置</el-button>
            </el-form-item>
        </el-form>

        <el-table v-loading="loading" :data="userList" @select="handleSelectionChange" @select-all="handleSelectionAll" ref="multipleTable" size="mini">
            <el-table-column type="selection" width="40"></el-table-column>
            <el-table-column label="用户ID" align="center" prop="userId" width="100" />
            <el-table-column label="用户名称" align="center" prop="userName" />
            <el-table-column label="所属部门" align="center" prop="deptName">
                <template slot-scope="scope">
                    <span>{{ scope.row.dept.deptName }}</span>
                </template>
            </el-table-column>
        </el-table>

        <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize" @pagination="getList" />

        <div slot="footer" style="width: 100%">
            <el-button type="primary" @click="handleEmitData">确 定</el-button>
            <el-button @click="cancel">取 消</el-button>
        </div>
    </el-dialog>
</template>

<script>
import { listUser } from '@/api/system/user';

export default {
    name: 'userList',
    data() {
        return {
            // 打开场景列表
            open: false,
            // 遮罩层
            loading: true,
            // 选中数组
            ids: [],
            // 非单个禁用
            single: true,
            // 非多个禁用
            multiple: true,
            // 显示搜索条件
            showSearch: true,
            // 总条数
            total: 0,
            // 场景联动表格数据
            userList: [],
            // 选中的用户
            selects: [],
            // 弹出层标题
            title: '',
            // 查询参数
            queryParams: {
                pageNum: 1,
                pageSize: 10,
                userName: null,
                deptId: null,
            },
            form: {},
        };
    },
    created() {},
    methods: {
        /** 确定后返回数据 */
        handleEmitData() {
            this.$emit('userEvent', this.selects);
            this.open = false;
        },
        /** 查询场景联动列表 */
        getList() {
            this.loading = true;
            listUser(this.queryParams).then((response) => {
                this.userList = response.rows;
                this.total = response.total;
                this.loading = false;
                // 设置场景选中
                this.userList.forEach((row) => {
                    this.$nextTick(() => {
                        if (this.ids.some((x) => x === row.userId)) {
                            this.$refs.multipleTable.toggleRowSelection(row, true);
                        }
                    });
                });
            });
        },
        // 取消按钮
        cancel() {
            this.open = false;
            this.ids = [];
            this.selects = [];
        },
        // 搜索按钮操作
        handleQuery() {
            this.queryParams.pageNum = 1;
            this.getList();
        },
        // 重置按钮操作
        handleResetQuery() {
            this.resetForm('queryForm');
            this.handleQuery();
        },
        // 多选框选中数据
        handleSelectionChange(selection, row) {
            // 场景ID是否存在于原始设备ID数组中
            let index = this.ids.indexOf(row.userId);
            // 是否选中
            let value = selection.indexOf(row);
            if (index == -1 && value != -1) {
                // 不存在且选中
                this.ids.push(row.userId);
                this.selects.push(row);
            } else if (index != -1 && value == -1) {
                // 存在且取消选中
                this.ids.splice(index, 1);
                this.selects.splice(index, 1);
            }
        },
        // 全选事件处理
        handleSelectionAll(selection) {
            for (let i = 0; i < this.userList.length; i++) {
                // 场景ID是否存在于原始设备ID数组中
                let index = this.ids.indexOf(this.userList[i].sceneId);
                // 是否选中
                let value = selection.indexOf(this.userList[i]);
                if (index == -1 && value != -1) {
                    // 不存在且选中
                    this.ids.push(this.userList[i].userId);
                    this.selects.push(this.userList[i]);
                } else if (index != -1 && value == -1) {
                    // 存在且取消选中
                    this.ids.splice(index, 1);
                    this.selects.splice(index, 1);
                }
            }
        },
    },
};
</script>

<style lang="scss" scoped></style>
