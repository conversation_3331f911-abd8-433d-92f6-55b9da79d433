<template>
  <div id="main" class="chart-container"></div>
</template>

<script>
import * as echarts from 'echarts';

export default {
  name: 'ChartComponent',
  data() {
    return {
      plantCap: [
        { name: '生活照明', value: '53%' },
        { name: '地下车库', value: '46%' },
        { name: '大楼外观', value: '5T' },
        { name: '景观照明', value: '95M' },
        { name: '标识照明', value: '57%' },
        { name: '商业照明', value: '43%' },
      ],
      datalist: [
        { offset: [56, 48], symbolSize: 110, color: this.getRadialGradient('rgb(67, 186, 255)') },
        { offset: [30, 70], symbolSize: 70, color: this.getRadialGradient('rgb(67, 186, 255)') },
        { offset: [5, 43], symbolSize: 60, color: this.getRadialGradient('rgb(27, 96, 255)') },
        { offset: [93, 30], symbolSize: 70, color: this.getRadialGradient('rgb(48, 191, 191)') },
        { offset: [26, 25], symbolSize: 65, color: this.getRadialGradient('rgb(0, 166, 166)') },
        { offset: [75, 75], symbolSize: 60, color: this.getRadialGradient('rgb(84, 136, 255)') },
      ],
      chartInstance: null
    };
  },
  mounted() {
    if (this.plantCap && this.datalist && this.plantCap.length === this.datalist.length) {
      this.initChart();
    } else {
      console.error('plantCap or datalist is not initialized correctly or their lengths do not match.');
    }
  },
  beforeDestroy() {
    if (this.chartInstance) {
      this.chartInstance.dispose();
      this.chartInstance = null;
    }
  },
  methods: {
    initChart() {
      const datas = this.prepareData();

      const option = {
        tooltip: {
          trigger: 'item',
          formatter: function (params) {
            return params.data.name;
          }
        },
        grid: {
          show: false,
          top: 10,
          bottom: 10
        },
        xAxis: {
          gridIndex: 0,
          type: 'value',
          show: false,
          min: 0,
          max: 100,
          nameLocation: 'middle',
          nameGap: 5
        },
        yAxis: {
          gridIndex: 0,
          min: 0,
          show: false,
          max: 100,
          nameLocation: 'middle',
          nameGap: 30
        },
        series: [{
          name: 'Effect Scatter',
          type: 'effectScatter',
          symbol: 'circle',
          symbolSize: function (val) {
            return val[2]; // 使用动态大小
          },
          rippleEffect: {
            period: 4,
            brushType: 'stroke',
          },
          data: datas
        }]
      };

      this.chartInstance = echarts.init(document.getElementById('main'));
      this.chartInstance.setOption(option);
    },
    prepareData() {
      const datas = [];
      for (let i = 0; i < this.plantCap.length; i++) {
        const item = this.plantCap[i];
        const itemToStyle = this.datalist[i];
        if (item && itemToStyle) {
          datas.push({
            name: `${item.value}\n${item.name}`,
            value: itemToStyle.offset.concat(itemToStyle.symbolSize), // 合并偏移量和符号大小
            label: {
              normal: {
                show: true,
                formatter: '{b}',
                // position: 'top',
                color: '#ffffff',
                lineHeight: 15,
                textStyle: {
                  fontSize: '12'
                }
              },
            },
            itemStyle: {
              normal: {
                color: itemToStyle.color,
                opacity: 1,
                borderWidth: '1',
                borderType: 'solid',
                borderColor: '#red',
                shadowColor: 'rgba(0, 0, 0, .6)',
                shadowBlur: 10,
              }
            },
          });
        }
      }
      return datas;
    },
    getRadialGradient(color) {
      return new echarts.graphic.RadialGradient(0.4, 0.3, 1, [
        { offset: 0, color: `rgba(${parseInt(color.slice(4, -1).split(',')[0])}, ${parseInt(color.slice(4, -1).split(',')[1])}, ${parseInt(color.slice(4, -1).split(',')[2])}, 0)` },
        { offset: 1, color: color }
      ]);
    }
  }
};
</script>

<style scoped>
.chart-container {
  width: 100%;
  height: 100%;
  position: relative;
}
</style>