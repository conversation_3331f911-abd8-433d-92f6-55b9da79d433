import request from '@/utils/request'

// 查询场景设备列表
export function listScenarioDevice(query) {
  return request({
    url: '/iot/scenarioDevice/list',
    method: 'get',
    params: query
  })
}

// 查询场景设备详细
export function getScenarioDevice(id) {
  return request({
    url: '/iot/scenarioDevice/' + id,
    method: 'get'
  })
}

// 新增场景设备
export function addScenarioDevice(data) {
  return request({
    url: '/iot/scenarioDevice',
    method: 'post',
    data: data
  })
}

// 修改场景设备
export function updateScenarioDevice(data) {
  return request({
    url: '/iot/scenarioDevice',
    method: 'put',
    data: data
  })
}

// 删除场景设备
export function delScenarioDevice(id) {
  return request({
    url: '/iot/scenarioDevice/' + id,
    method: 'delete'
  })
}

// 查询场景设备产品列表
export function listScenarioDeviceProduct(query) {
  return request({
    url: '/iot/scenarioDevice/scenarioDeviceProductList',
    method: 'get',
    params: query
  })
}


