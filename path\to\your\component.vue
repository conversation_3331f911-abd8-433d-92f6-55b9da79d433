<template>
  <div class="monitor-equipment-main">
    <dv-scroll-board :config="deviceTableConfig" style="width:100%;height:300px" />
  </div>
</template>

<script>
export default {
  data() {
    return {
      deviceTableConfig: {
        header: ['设备ID', '位置', '状态', '最后上报时间'],
        data: [],
        rowNum: 7,
        headerBGC: '#0f1325',
        headerHeight: 50,
        oddRowBGC: '#0f1325',
        evenRowBGC: '#171c33',
        index: true,
        columnWidth: [50],
        align: ['center']
      }
    };
  },
  mounted() {
    this.generateMockData();
    this.startUpdatingStatus();
  },
  beforeDestroy() {
    clearInterval(this.updateInterval);
  },
  methods: {
    generateMockData() {
      const mockData = Array.from({ length: 8 }, (_, index) => [
        `设备${index + 1}`,
        `位置${index + 1}`,
        Math.random() > 0.5 ? '在线' : '离线',
        this.getRandomTime()
      ]);
      this.deviceTableConfig.data = mockData;
    },
    getRandomTime() {
      const now = new Date();
      const randomMinutes = Math.floor(Math.random() * 60);
      now.setMinutes(now.getMinutes() - randomMinutes);
      return now.toLocaleTimeString();
    },
    startUpdatingStatus() {
      this.updateInterval = setInterval(() => {
        const randomIndex = Math.floor(Math.random() * this.deviceTableConfig.data.length);
        const newStatus = this.deviceTableConfig.data[randomIndex][2] === '在线' ? '离线' : '在线';
        this.deviceTableConfig.data[randomIndex][2] = newStatus;
        this.deviceTableConfig.data[randomIndex][3] = this.getRandomTime();
      }, 5000);
    }
  }
};
</script>

<style scoped>
.monitor-equipment-main {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}
</style>