<template>
  <!-- 批量导入设备 -->
  <el-dialog :close-on-click-modal="false" :title="upload.title" :visible.sync="upload.importDeviceDialog" width="550px"
    append-to-body>
    <div style="margin-top: -55px">
      <el-divider style="margin-top: -30px"></el-divider>
      <el-form label-position="top" :model="importForm" ref="importForm" :rules="importRules">
        <el-form-item label="所属机构" prop="deptId">
          <treeselect v-model="importForm.deptId" :options="deptOptions" :show-count="true" placeholder="请选择所属机构"
            @select="handleDeptChange" style="width: 100%" />
        </el-form-item>
        <el-form-item label="所属场景" prop="scenarioId">
          <el-select v-model="importForm.scenarioId" placeholder="请选择所属场景" clearable style="width: 100%"
            :disabled="!importForm.deptId" @change="handleScenarioChange">
            <el-option v-for="scenario in scenarioOptions" :key="scenario.scenarioId" :label="scenario.scenarioName"
              :value="scenario.scenarioId" />
          </el-select>
        </el-form-item>
        <el-form-item label="产品" prop="productId">
          <el-select v-model="importForm.productId" placeholder="请选择产品" style="width: 100%" filterable>
            <el-option v-for="item in productList" :key="item.value" :label="item.label" :value="item.value">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="上传文件" prop="fileList">
          <el-upload ref="upload" :limit="1" accept=".xlsx, .xls" :headers="upload.headers"
            v-model="importForm.fileList" :action="uploadUrl" :disabled="upload.isUploading"
            :on-progress="handleFileUploadProgress" :on-success="handleFileSuccess" :auto-upload="false"
            :on-change="handleChange" :on-remove="handleRemove" drag>
            <i class="el-icon-upload"></i>
            <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
            <div class="el-upload__tip" slot="tip">
              <div style="margin-top: 10px;">
                <span>提示:仅允许导入xls、xlsx格式文件。</span>
              </div>
            </div>
          </el-upload>
          <el-link type="primary" :underline="false" style="font-size:14px;vertical-align: baseline;"
            @click="importTemplate"><i class="el-icon-download"></i>下载设备导入模板</el-link>
        </el-form-item>
      </el-form>
    </div>
    <div slot="footer" class="dialog-footer">
      <el-button type="primary" @click="submitFileForm">确 定</el-button>
      <el-button @click="upload.importDeviceDialog = false">取 消</el-button>
    </div>
  </el-dialog>
</template>
<script>
import { listProduct } from "@/api/iot/product";
import { getToken } from "@/utils/auth";
import { deptsTreeSelect } from '@/api/system/user';
import { listScenario } from '@/api/iot/scenario';
import Treeselect from '@riophae/vue-treeselect';
import '@riophae/vue-treeselect/dist/vue-treeselect.css';
export default {
  name: 'batchImport',
  components: {
    Treeselect,
  },
  computed: {
    uploadUrl() {
      let url = this.upload.url;
      const params = [];

      if (this.importForm.productId) {
        params.push(`productId=${this.importForm.productId}`);
      }
      if (this.importForm.deptId) {
        params.push(`deptId=${this.importForm.deptId}`);
      }
      if (this.importForm.deptName) {
        params.push(`deptName=${encodeURIComponent(this.importForm.deptName)}`);
      }
      if (this.importForm.scenarioId) {
        params.push(`scenarioId=${this.importForm.scenarioId}`);
      }
      if (this.importForm.scenarioName) {
        params.push(`scenarioName=${encodeURIComponent(this.importForm.scenarioName)}`);
      }

      if (params.length > 0) {
        url += '?' + params.join('&');
      }

      return url;
    }
  },
  data() {
    return {
      type: 1,
      //导入表单
      importForm: {
        deptId: null,
        deptName: '',
        scenarioId: null,
        scenarioName: '',
        productId: null,
        fileList: [],
      },
      productList: [],
      // 机构选项
      deptOptions: [],
      // 场景选项
      scenarioOptions: [],
      file: null,
      // 批量导入参数
      upload: {
        // 是否显示弹出层
        importDeviceDialog: false,
        // 弹出层标题
        title: "批量导入",
        // 是否禁用上传
        isUploading: false,
        // 设置上传的请求头部
        headers: { Authorization: "Bearer " + getToken() },
        // 上传的地址
        url: process.env.VUE_APP_BASE_API + "/iot/device/importData"
      },
      // 批量导入表单校验
      importRules: {
        deptId: [{ required: true, message: '所属机构不能为空', trigger: 'change' }],
        scenarioId: [{ required: true, message: '所属场景不能为空', trigger: 'change' }],
        productId: [{ required: true, message: '产品不能为空', trigger: 'change' }],
        fileList: [
          { required: true, message: '请上传文件', trigger: 'change' }
        ]
      },
    };
  },
  created() {
    this.getProductList();
    this.getDeptTree();
  },
  methods: {
    /** 下载模板操作 */
    importTemplate() {
      this.download('/iot/device/uploadTemplate?type=' + this.type, {},
        `device_template_${new Date().getTime()}.xlsx`);
    },
    // 选择文件后给表单验证的prop字段赋值， 并且清除该字段的校验
    handleChange(file, fileList) {
      this.importForm.fileList = fileList;
      // 防止用户打开了文件选择框之后不选择文件而出现效验失败
      if (this.importForm.fileList) {
        this.$refs.importForm.clearValidate('fileList');
      }
    },
    // 删除文件后重新校验该字段
    handleRemove(file, fileList) {
      this.importForm.fileList = fileList;
      this.$refs.importForm.validateField('fileList');
    },
    // 文件上传中处理
    handleFileUploadProgress(event, file, fileList) {
      this.upload.isUploading = true;
    },
    // 文件上传成功处理
    handleFileSuccess(response, file, fileList) {
      this.upload.importDeviceDialog = false;
      this.upload.isUploading = false;
      this.$refs.upload.clearFiles();
      this.$alert("<div style='overflow: auto;overflow-x: hidden;max-height: 70vh;padding: 10px 20px 0;'>" + response.msg + "</div>", "导入结果", { dangerouslyUseHTMLString: true });
    },
    /** 查询产品列表 */
    getProductList() {
      this.loading = true;
      const params = {
        pageSize: 999,
      }
      listProduct(params).then(response => {
        this.productList = response.rows.map((item) => {
          return { value: item.productId, label: item.productName };
        });
        this.total = response.total;
        this.loading = false;
      });
    },
    /** 获取机构下拉树 */
    getDeptTree() {
      deptsTreeSelect().then((response) => {
        this.deptOptions = response.data;
      });
    },
    /** 机构选择改变 */
    handleDeptChange(node) {
      // 清空场景选择
      this.importForm.scenarioId = null;
      this.importForm.scenarioName = '';
      this.scenarioOptions = [];

      if (node && node.id) {
        // 根据选中的机构获取对应的场景列表
        this.getScenarioList(node.id);
        // 设置机构名称
        this.importForm.deptName = node.label || '';
      }
    },
    /** 获取场景列表 */
    getScenarioList(tenantId) {
      const params = {
        tenantId: tenantId
      };
      listScenario(params).then((response) => {
        this.scenarioOptions = response.rows || response.data || [];
      });
    },
    /** 场景选择改变 */
    handleScenarioChange(value) {
      if (value) {
        // 设置场景名称
        const selectedScenario = this.scenarioOptions.find(item => item.scenarioId === value);
        this.importForm.scenarioName = selectedScenario ? selectedScenario.scenarioName : '';
      } else {
        this.importForm.scenarioName = '';
      }
    },
    // 提交上传文件
    submitFileForm() {
      this.$refs['importForm'].validate((valid) => {
        if (valid) {
          this.$refs.upload.submit();
          this.upload.importDeviceDialog = false;
        }
      });
    },
  },
};
</script>
