<template>
  <div style="padding: 6px">
    <tool-form :queryParamsProp="queryParams" :myGroupList="myGroupList" :showSearch.sync="showSearch"
      :showType="showType" @search="handleQuery" @reset="resetQuery" @queryTable="getList"
      @changeShowType="handleChangeShowType" @command="handleCommand" @export="handleExport"
      @batchDelete="handleBatchDelete"></tool-form>

    <el-card style="padding-bottom: 100px" v-if="showType == 'list'">
      <el-table v-loading="loading" :data="deviceList" border @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55"></el-table-column>
        <el-table-column label="编号" align="center" header-align="center" prop="deviceId" width="50" />
        <el-table-column label="设备名称" align="center" header-align="center" prop="deviceName" min-width="120" />
        <el-table-column label="设备编号" align="center" prop="serialNumber" min-width="130" />
        <el-table-column label="所属产品" align="center" prop="productName" min-width="120" />
        <el-table-column label="状态" align="center" prop="status" width="80">
          <template slot-scope="scope">
            <el-tag size="small" type="info" v-if="scope.row.status === 1">未激活</el-tag>
            <el-tag size="small" type="warning" v-else-if="scope.row.status === 2">禁用</el-tag>
            <el-tag size="small" type="success" v-else-if="scope.row.status === 3">在线</el-tag>
            <el-tag size="small" type="danger" v-else-if="scope.row.status === 4">离线</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="信号" align="center" prop="rssi" width="60">
          <template slot-scope="scope">
            <svg-icon v-if="scope.row.status == 3 && scope.row.rssi >= '-55'" icon-class="wifi_4" />
            <svg-icon v-else-if="scope.row.status == 3 && scope.row.rssi >= '-70' && scope.row.rssi < '-55'"
              icon-class="wifi_3" />
            <svg-icon v-else-if="scope.row.status == 3 && scope.row.rssi >= '-85' && scope.row.rssi < '-70'"
              icon-class="wifi_2" />
            <svg-icon v-else-if="scope.row.status == 3 && scope.row.rssi >= '-100' && scope.row.rssi < '-85'"
              icon-class="wifi_1" />
            <svg-icon v-else icon-class="wifi_0" />
          </template>
        </el-table-column>

        <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="200">
          <template slot-scope="scope">
            <el-button type="danger" size="small" style="padding: 5px" icon="el-icon-delete"
              @click="handleDelete(scope.row)" v-hasPermi="['iot:device:remove']">删除</el-button>
            <el-button type="success" size="mini" style="padding: 5px 15px" icon="el-icon-odometer"
              @click="handleRunDevice(scope.row)" v-hasPermi="['iot:device:query']">运行状态</el-button>
          </template>
        </el-table-column>
      </el-table>
      <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize"
        :pageSizes="[12, 24, 36, 60]" @pagination="getList" />
    </el-card>

    <el-card style="padding-bottom: 100px" v-if="showType == 'card'">
      <el-row :gutter="30" v-loading="loading">
        <el-col :xs="24" :sm="12" :md="12" :lg="8" :xl="6" v-for="(item, index) in deviceList" :key="index"
          style="margin-bottom: 30px; text-align: center">
          <el-card :body-style="{ padding: '20px' }" shadow="always" class="card-item">
            <el-row type="flex" :gutter="10" justify="space-between">
              <el-col :span="2">
                <el-checkbox :value="selectedIds.includes(item.deviceId)"
                  @change="(val) => handleCardSelection(val, item.deviceId)"></el-checkbox>
              </el-col>
              <el-col :span="18" style="text-align: left">
                <el-link type="" :underline="false" @click="handleDeviceDetail(item)"
                  style="font-weight: bold; font-size: 16px; line-height: 32px">
                  <el-tooltip class="item" effect="dark" content="分享的设备" placement="top-start">
                    <svg-icon icon-class="share" style="font-size: 20px" v-if="item.isOwner != 1" />
                  </el-tooltip>
                  <svg-icon icon-class="device" v-if="item.isOwner == 1" />
                  <span style="margin: 0 5px">{{ item.deviceName }}</span>
                </el-link>
              </el-col>
              <el-col :span="1.5" style="font-size: 20px; padding-top: 5px; cursor: pointer">
                <svg-icon icon-class="qrcode" @click="openSummaryDialog(item)" v-hasPermi="['iot:device:query']" />
              </el-col>
              <el-col :span="3">
                <div style="font-size: 28px; color: #ccc">
                  <svg-icon v-if="item.status == 3 && item.rssi >= '-55'" icon-class="wifi_4" />
                  <svg-icon v-else-if="item.status == 3 && item.rssi >= '-70' && item.rssi < '-55'"
                    icon-class="wifi_3" />
                  <svg-icon v-else-if="item.status == 3 && item.rssi >= '-85' && item.rssi < '-70'"
                    icon-class="wifi_2" />
                  <svg-icon v-else-if="item.status == 3 && item.rssi >= '-100' && item.rssi < '-85'"
                    icon-class="wifi_1" />
                  <svg-icon v-else icon-class="wifi_0" />
                </div>
              </el-col>
            </el-row>
            <el-row :gutter="10">
              <el-col :span="17">
                <div style="text-align: left; line-height: 40px; white-space: nowrap">
                  <div v-if="item.status === 1" style="display: inline-block">
                    <el-tag size="small" type="info">未激活</el-tag>
                  </div>
                  <div v-else-if="item.status === 2" style="display: inline-block">
                    <el-tag size="small" type="warning">禁用</el-tag>
                  </div>
                  <div v-else-if="item.status === 3" style="display: inline-block">
                    <el-tag size="small" type="success">在线</el-tag>
                  </div>
                  <div v-else-if="item.status === 4" style="display: inline-block">
                    <el-tag size="small" type="danger">离线</el-tag>
                  </div>
                  <span style="display: inline-block; margin: 0 10px">
                    <el-tag type="primary" size="small" v-if="item.protocolCode">{{ item.protocolCode }}</el-tag>
                  </span>
                  <el-tag type="primary" size="small" v-if="item.transport">{{ item.transport }}</el-tag>
                </div>
                <el-descriptions :column="1" size="mini" style="white-space: nowrap">
                  <el-descriptions-item label="编号">
                    {{ item.serialNumber }}
                  </el-descriptions-item>
                  <el-descriptions-item label="产品">
                    {{ item.productName }}
                  </el-descriptions-item>
                  <el-descriptions-item label="激活时间">
                    {{ parseTime(item.activeTime, '{y}-{m}-{d}') }}
                  </el-descriptions-item>
                </el-descriptions>
              </el-col>
              <el-col :span="7">
                <div style="margin-top: 10px">
                  <el-image style="width: 100%; height: 100px; border-radius: 10px" lazy
                    :preview-src-list="[baseUrl + item.imgUrl]" :src="baseUrl + item.imgUrl" fit="cover"
                    v-if="item.imgUrl != null && item.imgUrl != ''"></el-image>
                  <el-image style="width: 100%; height: 100px; border-radius: 10px"
                    :preview-src-list="[require('@/assets/images/gateway.png')]"
                    :src="require('@/assets/images/gateway.png')" fit="cover"
                    v-else-if="item.deviceType == 2"></el-image>
                  <el-image style="width: 100%; height: 100px; border-radius: 10px"
                    :preview-src-list="[require('@/assets/images/video.png')]"
                    :src="require('@/assets/images/video.png')" fit="cover" v-else-if="item.deviceType == 3"></el-image>
                  <el-image style="width: 100%; height: 100px; border-radius: 10px"
                    :preview-src-list="[require('@/assets/images/product.png')]"
                    :src="require('@/assets/images/product.png')" fit="cover" v-else></el-image>
                </div>
              </el-col>
            </el-row>
            <el-button-group style="margin-top: 15px">
              <el-button type="danger" size="mini" style="padding: 5px 10px" icon="el-icon-delete"
                @click="handleDelete(item)" v-hasPermi="['iot:device:remove']">删除</el-button>
              <el-button type="primary" size="mini" style="padding: 5px 15px" icon="el-icon-view"
                @click="handleEditDevice(item, 'basic')" v-hasPermi="['iot:device:query']">查看</el-button>
              <el-button type="success" size="mini" style="padding: 5px 15px" icon="el-icon-odometer"
                @click="handleRunDevice(item)" v-hasPermi="['iot:device:query']">运行状态</el-button>
            </el-button-group>
          </el-card>
        </el-col>
      </el-row>
      <el-empty description="暂无数据，请添加设备" v-if="total == 0"></el-empty>
      <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize"
        :pageSizes="[12, 24, 36, 60]" @pagination="getList" />
    </el-card>
    <!-- 二维码 -->
    <el-dialog :close-on-click-modal="false" :visible.sync="openSummary" width="300px" append-to-body>
      <div style="border: 1px solid #ccc; width: 220px; text-align: center; margin: 0 auto; margin-top: -15px">
        <vue-qr :text="qrText" :size="200"></vue-qr>
        <div style="padding-bottom: 10px">设备二维码</div>
      </div>
    </el-dialog>
    <!-- 导入分配 -->
    <allotImport ref="allotImport" @save="saveAllotDialog"></allotImport>
  </div>
</template>

<script>
import vueQr from 'vue-qr';
import { listDeviceShort, delDevice } from '@/api/iot/device';
import { listGroup } from '@/api/iot/group';
import { delSipDeviceBySipId } from '@/api/iot/sipdevice';
import auth from '@/plugins/auth';
import ToolForm from './components/toolForm';
import Pagination from "@/components/Pagination";
import allotImport from '../../iot/device/allot-import-dialog';

export default {
  name: 'Device',
  dicts: ['iot_device_status', 'iot_is_enable', 'iot_location_way', 'iot_transport_type'],
  components: {
    vueQr,
    ToolForm,
    Pagination,
    allotImport
  },
  data() {
    return {
      // 二维码内容
      qrText: 'seefy',
      // 打开设备配置对话框
      openSummary: false,
      // 显示搜索条件
      showSearch: true,
      // 展示方式
      showType: 'list',
      // 遮罩层
      loading: true,
      // 总条数
      total: 0,
      // 设备列表数据
      deviceList: [],
      // 我的分组列表数据
      myGroupList: [],
      // 根路径
      baseUrl: process.env.VUE_APP_BASE_API,
      // 定时刷新定时器
      timer: null,
      // 选中的设备ID数组
      selectedIds: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 12,
        showChild: true,
        deviceName: null,
        productId: null,
        groupId: null,
        productName: null,
        userId: null,
        userName: null,
        tenantId: null,
        tenantName: null,
        serialNumber: null,
        status: null,
        networkAddress: null,
        activeTime: null,
      },
      dict: {
        type: {
          iot_device_status: [],
          iot_is_enable: [],
          iot_location_way: [],
          iot_transport_type: [],
        },
      },
    };
  },
  created() {
    // 产品筛选
    let productId = this.$route.query.productId;
    if (productId != null) {
      this.queryParams.productId = Number(productId);
      this.queryParams.groupId = null;
      this.queryParams.serialNumber = null;
    }
    // 分组筛选
    let groupId = this.$route.query.groupId;
    if (groupId != null) {
      this.queryParams.groupId = Number(groupId);
      this.queryParams.productId = null;
      this.queryParams.serialNumber = null;
    }
    // 设备编号筛选
    let sn = this.$route.query.sn;
    if (sn != null) {
      this.queryParams.serialNumber = sn;
      this.queryParams.productId = null;
      this.queryParams.groupId = null;
    }
    this.getGroupList();
    this.connectMqtt();
    // 添加可见性变化监听
    document.addEventListener('visibilitychange', this.handleVisibilityChange);
  },
  mounted() {
    // 启动定时刷新
    this.startRefreshTimer();
  },
  beforeDestroy() {
    // 清理定时器和事件监听
    this.stopRefreshTimer();
    document.removeEventListener('visibilitychange', this.handleVisibilityChange);
  },
  activated() {
    const time = this.$route.query.t;
    if (time != null && time != this.uniqueId) {
      this.uniqueId = time;
      // 页码筛选
      let pageNum = this.$route.query.pageNum;
      if (pageNum != null) {
        this.queryParams.pageNum = Number(pageNum);
      }
      // 产品筛选
      let productId = this.$route.query.productId;
      if (productId != null) {
        this.queryParams.productId = Number(productId);
        this.queryParams.groupId = null;
        this.queryParams.serialNumber = null;
      }
      // 分组筛选
      let groupId = this.$route.query.groupId;
      if (groupId != null) {
        this.queryParams.groupId = Number(groupId);
        this.queryParams.productId = null;
        this.queryParams.serialNumber = null;
      }
      // 设备编号筛选
      let sn = this.$route.query.sn;
      if (sn != null) {
        this.queryParams.serialNumber = sn;
        this.queryParams.productId = null;
        this.queryParams.groupId = null;
      }
      this.getList();
      // 重启定时刷新
      this.startRefreshTimer();
    }
  },
  deactivated() {
    // 当组件被缓存时停止定时器
    this.stopRefreshTimer();
  },
  methods: {
    /* 启动定时刷新 */
    startRefreshTimer() {
      // 先清除现有定时器
      this.stopRefreshTimer();
      // 创建新定时器，每5秒刷新一次
      this.timer = setInterval(() => {
        // 只有在页面可见时才执行刷新
        if (!document.hidden) {
          this.refreshList();
        }
      }, 5000);
    },
    /* 停止定时刷新 */
    stopRefreshTimer() {
      if (this.timer) {
        clearInterval(this.timer);
        this.timer = null;
      }
    },
    /* 处理页面可见性变化 */
    handleVisibilityChange() {
      if (document.hidden) {
        // 页面不可见时停止定时器
        this.stopRefreshTimer();
      } else {
        // 页面可见时重启定时器
        this.startRefreshTimer();
        // 立即刷新一次数据
        this.refreshList();
      }
    },
    /* 刷新列表数据 */
    refreshList() {
      // 不显示加载动画，静默刷新
      this.queryParams.params = {};
      listDeviceShort(this.queryParams).then((response) => {
        // 对比数据是否有变化
        const newData = response.rows;
        const oldData = this.deviceList;
        let hasChanged = false;

        // 简单对比数据长度或关键状态
        if (newData.length !== oldData.length) {
          hasChanged = true;
        } else {
          // 更详细的比较，检查每个设备的关键属性
          for (let i = 0; i < newData.length; i++) {
            if (newData[i].status !== oldData[i].status ||
              newData[i].rssi !== oldData[i].rssi) {
              hasChanged = true;
              break;
            }
          }
        }

        // 只有数据发生变化时才更新DOM
        if (hasChanged) {
          this.deviceList = newData;
          this.total = response.total;
          // 订阅消息
          if (this.deviceList && this.deviceList.length > 0) {
            this.mqttSubscribe(this.deviceList);
          }
        }
      });
    },
    /* 连接Mqtt消息服务器 */
    async connectMqtt() {
      if (this.$mqttTool.client == null) {
        await this.$mqttTool.connect();
      }
      this.mqttCallback();
      this.getList();
    },
    /* Mqtt回调处理  */
    mqttCallback() {
      this.$mqttTool.client.on('message', (topic, message, buffer) => {
        let topics = topic.split('/');
        let productId = topics[1];
        let deviceNum = topics[2];
        message = JSON.parse(message.toString());
        if (!message) {
          return;
        }
        if (topics[3] == 'status') {
          console.log('接收到【设备状态】主题：', topic);
          console.log('接收到【设备状态】内容：', message);
          // 更新列表中设备的状态
          for (let i = 0; i < this.deviceList.length; i++) {
            if (this.deviceList[i].serialNumber == deviceNum) {
              this.deviceList[i].status = message.status;
              this.deviceList[i].isShadow = message.isShadow;
              this.deviceList[i].rssi = message.rssi;
              return;
            }
          }
        }
      });
    },
    // 新增设备操作触发
    handleCommand(command) {
      switch (command) {
        case 'handleSelectAllot':
          this.handleSelectAllot();
          break;
        case 'handleImportAllot':
          this.handleImportAllot();
          break;
        default:
          break;
      }
    },
    //跳转选择分配设备页面
    handleSelectAllot() {
      this.$router.push({
        path: '/iot/device-select-allot',
      });
    },
    saveAllotDialog() {
      this.getList();
    },
    //导入分配设备
    handleImportAllot() {
      this.$refs.allotImport.upload.importAllotDialog = true;
      this.$refs.allotImport.allotForm.productId = null;
      this.$refs.allotImport.allotForm.deptId = null;
    },
    openSummaryDialog(row) {
      let json = {
        type: 1, // 1=扫码关联设备
        deviceNumber: row.serialNumber,
        productId: row.productId,
        productName: row.productName,
      };
      this.qrText = JSON.stringify(json);
      this.openSummary = true;
    },
    /* 订阅消息 */
    mqttSubscribe(list) {
      // 订阅当前页面设备状态和实时监测
      let topics = [];
      for (let i = 0; i < list.length; i++) {
        let topicStatus = '/' + '+' + '/' + list[i].serialNumber + '/status/post';
        topics.push(topicStatus);
      }
      this.$mqttTool.subscribe(topics);
    },
    /** 查询设备分组列表 */
    getGroupList() {
      this.loading = true;
      let queryParams = {
        pageSize: 30,
        pageNum: 1,
        userId: this.$store.state.user.userId,
      };
      listGroup(queryParams).then((response) => {
        this.myGroupList = response.rows;
      });
    },
    /** 查询所有简短设备列表 */
    getList() {
      this.loading = true;
      this.queryParams.params = {};
      // this.getGroupList();
      listDeviceShort(this.queryParams).then((response) => {
        this.deviceList = response.rows;
        this.total = response.total;
        // 订阅消息
        if (this.deviceList && this.deviceList.length > 0) {
          this.mqttSubscribe(this.deviceList);
        }
        this.loading = false;
      });
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
      // 条件变更后重新启动定时器
      this.startRefreshTimer();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.queryParams.productId = null;
      this.queryParams.groupId = null;
      this.queryParams.serialNumber = null;
      this.resetForm('queryForm');
      this.handleQuery();
      // 条件变更后重新启动定时器
      this.startRefreshTimer();
    },
    /** 切换显示方式 */
    handleChangeShowType() {
      this.showType = this.showType == 'card' ? 'list' : 'card';
    },
    // 点击名称查看
    handleDeviceDetail(item) {
      if (auth.hasPermi('iot:device:query')) {
        this.handleEditDevice(item);
      }
    },
    /** 修改按钮操作 */
    handleEditDevice(row, activeName) {
      let deviceId = 0;
      let isSubDev = 0;
      if (row != 0) {
        deviceId = row.deviceId || this.ids;
        isSubDev = row.subDeviceCount > 0 ? 1 : 0;
      }
      this.$router.push({
        path: '/test/device-edit',
        query: {
          deviceId: deviceId,
          isSubDev: isSubDev,
          pageNum: this.queryParams.pageNum,
          activeName: activeName,
        },
      });
    },
    /** 运行状态按钮操作 */
    handleRunDevice(row) {
      let deviceId = 0;
      let isSubDev = 0;
      if (row != 0) {
        deviceId = row.deviceId || this.ids;
        isSubDev = row.subDeviceCount > 0 ? 1 : 0;
      }
      const activeName = row.deviceType === 3 ? 'sipChannel' : 'runningStatus';
      this.$router.push({
        path: '/test/device-edit',
        query: {
          deviceId,
          isSubDev,
          pageNum: this.queryParams.pageNum,
          activeName,
        },
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const deviceIds = row.deviceId || this.ids;
      this.$modal
        .confirm('是否确认删除设备编号为"' + deviceIds + '"的数据项？')
        .then(function () {
          if (row.deviceType === 3) {
            delSipDeviceBySipId(row.serialNumber);
          }
          return delDevice(deviceIds);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess('删除成功');
        })
        .catch(() => { });
    },
    /** 表格多选框选中数据 */
    handleSelectionChange(selection) {
      this.selectedIds = selection.map(item => item.deviceId);
    },
    /** 卡片视图选择设备 */
    handleCardSelection(val, deviceId) {
      if (val) {
        // 如果选中，将deviceId添加到selectedIds数组中
        if (!this.selectedIds.includes(deviceId)) {
          this.selectedIds.push(deviceId);
        }
      } else {
        // 如果取消选中，从selectedIds数组中移除deviceId
        this.selectedIds = this.selectedIds.filter(id => id !== deviceId);
      }
    },
    /** 批量删除按钮操作 */
    handleBatchDelete() {
      if (this.selectedIds.length === 0) {
        this.$modal.msgError('请至少选择一条记录');
        return;
      }

      const deviceIds = this.selectedIds.join(',');
      this.$modal
        .confirm('是否确认批量删除选中的' + this.selectedIds.length + '条数据？')
        .then(function () {
          return delDevice(deviceIds);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess('批量删除成功');
          this.selectedIds = [];
        })
        .catch(() => { });
    },
    handleExport() {
      this.download(
        "/iot/device/testExport",
        {
          ...this.queryParams,
        },
        `device_${new Date().getTime()}.xlsx`
      );
    }
  },
};
</script>

<style scoped>
.card-item {
  border-radius: 15px;
}

::v-deep .el-upload-dragger {
  width: 510px;
}

.el-dropdown-menu__item {
  font-size: 12px;
}
</style>
