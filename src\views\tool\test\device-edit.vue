<template>
  <el-card style="margin: 6px; padding-bottom: 100px">
    <el-tabs v-model="activeName" tab-position="left" @tab-click="tabChange" style="padding: 10px; min-height: 400px">
      <el-tab-pane name="basic">
        <span slot="label">* 基本信息</span>
        <el-form ref="form" :model="form" :rules="rules" label-width="100px">
          <el-row :gutter="100">
            <el-col :xs="24" :sm="24" :md="24" :lg="12" :xl="8">
              <el-form-item label="设备名称" prop="deviceName">
                <el-input v-model="form.deviceName" placeholder="请输入设备名称">
                  <el-button slot="append" @click="openSummaryDialog" v-if="form.deviceId != 0">摘要</el-button>
                </el-input>
              </el-form-item>
              <el-form-item label="" prop="productName">
                <template slot="label">
                  <span style="color: red">*</span>
                  所属产品
                </template>
                <el-input readonly v-model="form.productName" placeholder="请选择产品" :disabled="form.status != 1">
                  <el-button slot="append" @click="selectProduct()" :disabled="form.status != 1">选择</el-button>
                </el-input>
              </el-form-item>
              <el-form-item label="" prop="serialNumber">
                <template slot="label">
                  <span style="color: red">*</span>
                  设备编号
                </template>
                <el-input v-model="form.serialNumber" placeholder="请输入设备编号" :disabled="form.status != 1" maxlength="32">
                  <el-button v-if="form.deviceType !== 3" slot="append" @click="generateNum" :loading="genDisabled"
                    :disabled="form.status != 1" v-hasPermi="['iot:device:add']">生成</el-button>
                  <el-button v-if="form.deviceType === 3" slot="append" @click="genSipID()" :disabled="form.status != 1"
                    v-hasPermi="['iot:device:add']">生成</el-button>
                </el-input>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>

        <el-form label-width="100px" style="margin-top: 50px">
          <el-form-item style="text-align: center; margin-left: -100px; margin-top: 10px">
            <el-button type="primary" @click="submitForm" v-hasPermi="['iot:device:edit']" v-show="form.deviceId != 0">修
              改</el-button>
            <el-button type="primary" @click="submitForm" v-hasPermi="['iot:device:add']" v-show="form.deviceId == 0">新
              增</el-button>
          </el-form-item>
        </el-form>
      </el-tab-pane>

      <el-tab-pane name="runningStatus" v-if="form.deviceType !== 3">
        <span slot="label">运行状态</span>
        <real-time-status ref="realTimeStatus" :device="form" @statusEvent="getDeviceStatusData($event)"
          v-if="isSubDev" />
        <running-status ref="runningStatus" :device="form" @statusEvent="getDeviceStatusData($event)" v-else />
      </el-tab-pane>

      <!-- 用于设置间距 -->
      <el-tab-pane disabled>
        <span slot="label">
          <div style="margin-top: 350px"></div>
        </span>
      </el-tab-pane>
      <el-tab-pane name="deviceReturn" disabled>
        <span slot="label">
          <el-button type="info" size="mini" @click="goBack()">返回列表</el-button>
        </span>
      </el-tab-pane>
    </el-tabs>
  </el-card>
</template>

<script>
import JsonViewer from 'vue-json-viewer';
import 'vue-json-viewer/style.css';
import runningStatus from './running-status';
import realTimeStatus from './realTime-status';
import vueQr from 'vue-qr';
import { deviceSynchronization, getDevice, addDevice, updateDevice, generatorDeviceNum, getMqttConnect } from '@/api/iot/device';
import { getDeviceDetectStatus } from '@/api/iot/device';
import { cacheJsonThingsModel } from '@/api/iot/model';
import { getDeviceTemp } from '@/api/iot/temp';
import RealTimeStatus from '@/views/iot/device/realTime-status';
import { clientOut } from '@/api/iot/netty';

export default {
  name: 'DeviceEdit',
  dicts: ['iot_device_status', 'iot_location_way'],
  components: {
    runningStatus,
    JsonViewer,
    vueQr,
  },
  watch: {
    activeName(val) {
      if (val == 'deviceStastic') {
        this.$nextTick(() => {
          // TODO 重置统计表格的尺寸
        });
      }
    },
  },
  computed: {
    deviceStatus: {
      set(val) {
        if (val == 1) {
          // 1-未激活，2-禁用，3-在线，4-离线
          this.form.status = 2;
        } else if (val == 0) {
          this.form.status = 4;
        } else {
          this.form.status = this.oldDeviceStatus;
        }
      },
      get() {
        if (this.form.status == 2) {
          return 1;
        }
        return 0;
      },
    },
  },
  data() {
    return {
      // 二维码内容
      qrText: 'seefy',
      // 打开设备配置对话框
      openSummary: false,
      //二维码
      openCode: false,
      openViewMqtt: false,
      // 生成设备编码是否禁用
      genDisabled: false,
      // 选中选项卡
      activeName: 'basic',
      //查看mqtt参数
      mqttList: [],
      // 遮罩层
      loading: true,
      // 设备开始状态
      oldDeviceStatus: null,
      deviceId: '',
      channelId: '',
      // 表单参数
      form: {
        productId: 0,
        status: 1,
        locationWay: 1,
        firmwareVersion: 1.0,
        serialNumber: '',
        deviceType: 1,
        isSimulate: 0,
      },
      //mqtt参数查看
      listQuery: {
        clientId: 0,
        username: '',
        passwd: '',
        port: '',
      },
      openTip: false,
      openServerTip: false,
      serverType: 1,
      //用于判断是否是设备组(modbus)
      isSubDev: false,
      // 设备摘要
      summary: [],
      // 地址
      baseUrl: process.env.VUE_APP_BASE_API,
      // 地图相关
      map: null,
      mk: null,
      latitude: '',
      longitude: '',
      // 表单校验
      rules: {
        deviceName: [
          {
            required: true,
            message: '设备名称不能为空',
            trigger: 'blur',
          },
          {
            min: 2,
            max: 32,
            message: '设备名称长度在 2 到 32 个字符',
            trigger: 'blur',
          },
        ],
        firmwareVersion: [
          {
            required: true,
            message: '固件版本不能为空',
            trigger: 'blur',
          },
        ],
      },
      isMediaDevice: false,
      // 定时获取设备状态的定时器
      statusTimer: null,
      // 页面是否可见
      isPageVisible: true,
    };
  },
  created() {
    let activeName = this.$route.query.activeName;
    if (activeName != null && activeName != '') {
      this.activeName = activeName;
    }
    // 获取设备信息
    this.form.deviceId = this.$route.query && this.$route.query.deviceId;
    if (this.form.deviceId != 0) {
      this.connectMqtt();
      this.getDevice(this.form.deviceId);
    }
    this.isSubDev = this.$route.query.isSubDev == 1 ? true : false;

    // 添加页面可见性监听
    this.addVisibilityListener();
  },
  activated() {
    // 跳转选项卡
    let activeName = this.$route.query.activeName;
    if (activeName != null && activeName != '') {
      this.activeName = activeName;
    }

    // 页面激活时，如果当前在运行状态选项卡且页面可见，启动定时器
    if (this.activeName === 'runningStatus' && this.isPageVisible && this.form.deviceId) {
      this.startStatusTimer();
    }
  },
  deactivated() {
    // 页面失活时，停止定时器
    this.stopStatusTimer();
  },
  destroyed() {
    // 取消订阅主题
    this.mqttUnSubscribe(this.form);
    // 组件销毁时，停止定时器并移除页面可见性监听
    this.stopStatusTimer();
    this.removeVisibilityListener();
  },
  methods: {
    // 添加页面可见性监听
    addVisibilityListener() {
      document.addEventListener('visibilitychange', this.handleVisibilityChange);
    },

    // 移除页面可见性监听
    removeVisibilityListener() {
      document.removeEventListener('visibilitychange', this.handleVisibilityChange);
    },

    // 处理页面可见性变化
    handleVisibilityChange() {
      if (document.hidden) {
        // 页面不可见时，停止定时器
        this.isPageVisible = false;
        this.stopStatusTimer();
      } else {
        // 页面可见时，如果当前在运行状态选项卡，启动定时器
        this.isPageVisible = true;
        if (this.activeName === 'runningStatus' && this.form.deviceId) {
          this.startStatusTimer();
        }
      }
    },

    // 启动获取设备状态的定时器
    startStatusTimer() {
      // 确保不会重复启动定时器
      this.stopStatusTimer();

      // 立即执行一次
      this.refreshDeviceStatus();

      // 设置5秒定时执行
      this.statusTimer = setInterval(() => {
        this.refreshDeviceStatus();
      }, 3000);
    },

    // 停止获取设备状态的定时器
    stopStatusTimer() {
      if (this.statusTimer) {
        clearInterval(this.statusTimer);
        this.statusTimer = null;
      }
    },

    // 刷新设备状态
    async refreshDeviceStatus() {
      if (this.form && this.form.deviceId) {
        const thingsModels = await this.getDeviceStatus(this.form);
        if (thingsModels) {
          if (this.isSubDev && this.$refs.realTimeStatus) {
            // 如果是子设备且引用存在，更新实时状态组件
            this.$refs.realTimeStatus.deviceInfo.thingsModels = thingsModels;
            this.$refs.realTimeStatus.$nextTick(() => {
              if (this.$refs.realTimeStatus.MonitorChart) {
                this.$refs.realTimeStatus.MonitorChart();
              }
            });
          } else if (this.$refs.runningStatus) {
            // 更新运行状态组件
            this.$refs.runningStatus.deviceInfo.thingsModels = thingsModels;
            this.$refs.runningStatus.$nextTick(() => {
              this.$refs.runningStatus.MonitorChart();
            });
          }
        }
      }
    },

    /* 连接Mqtt消息服务器 */
    async connectMqtt() {
      if (this.$mqttTool.client == null) {
        await this.$mqttTool.connect(this.vuex_token);
      }
      this.mqttCallback();
    },

    /* Mqtt回调处理  */
    mqttCallback() {
      this.$mqttTool.client.on('message', (topic, message, buffer) => {
        let topics = topic.split('/');
        let productId = topics[1];
        let deviceNum = topics[2];
        message = JSON.parse(message.toString());
        if (!message) {
          return;
        }
        if (topics[3] == 'status' || topics[2] == 'status') {
          console.log('接收到【设备状态-详情】主题：', topic);
          console.log('接收到【设备状态-详情】内容：', message);
          // 更新列表中设备的状态
          if (this.form.serialNumber == deviceNum) {
            this.oldDeviceStatus = message.status;
            this.form.status = message.status;
            this.form.isShadow = message.isShadow;
            this.form.rssid = message.rssid;
          }
        }
        //不是modbus不转发到子页面，其他设备的页面有回调方法
        if (this.isSubDev) {
          /*发送设备上报到子模块*/
          if (topic.endsWith('ws/service')) {
            this.$busEvent.$emit('updateData', {
              serialNumber: topics[2],
              productId: this.form.productId,
              data: message,
            });
          }
          if (topic.endsWith('service/reply')) {
            this.$busEvent.$emit('updateLog', {
              serialNumber: topics[2],
              productId: this.form.productId,
              data: message,
            });
          }
        }
        /*发送设备上报到子模块*/
        if (topic.endsWith('ws/post/simulate')) {
          this.$busEvent.$emit('logData', {
            serialNumber: topics[1],
            productId: this.form.productId,
            data: message,
          });
        }
      });
    },

    /** Mqtt订阅主题 */
    mqttSubscribe(device) {
      // 订阅当前设备状态和实时监测
      let topicStatus = '/' + device.productId + '/' + device.serialNumber + '/status/post';
      let topicProperty = '/' + device.productId + '/' + device.serialNumber + '/property/post';
      let topicFunction = '/' + device.productId + '/' + device.serialNumber + '/function/post';
      let topicMonitor = '/' + device.productId + '/' + device.serialNumber + '/monitor/post';
      let topicReply = '/' + device.productId + '/' + device.serialNumber + '/service/reply';
      let topics = [];
      let serviceTop = '/' + device.productId + '/' + device.serialNumber + '/ws/service';
      topics.push(serviceTop);

      topics.push(topicStatus);
      topics.push(topicFunction);
      topics.push(topicMonitor);
      topics.push(topicReply);
      /*modbus设备不订阅此topic*/
      if (!this.isSubDev) {
        // topics.push(topicProperty);
      }
      this.$mqttTool.subscribe(topics);
    },

    /** Mqtt取消订阅主题 */
    mqttUnSubscribe(device) {
      if (!device || !device.productId || !device.serialNumber) {
        return;
      }
      // 订阅当前设备状态和实时监测
      let topicStatus = '/' + device.productId + '/' + device.serialNumber + '/status/post';
      let topicProperty = '/' + device.productId + '/' + device.serialNumber + '/property/post';
      let topicFunction = '/' + device.productId + '/' + device.serialNumber + '/function/post';
      let topicMonitor = '/' + device.productId + '/' + device.serialNumber + '/monitor/post';
      let topicReply = '/' + device.productId + '/' + device.serialNumber + '/service/reply';
      let topics = [];
      let serviceTop = '/' + device.productId + '/' + device.serialNumber + '/ws/service';
      topics.push(serviceTop);

      topics.push(topicStatus);
      topics.push(topicFunction);
      topics.push(topicMonitor);
      topics.push(topicReply);
      /*modbus设备不订阅此topic*/
      if (!this.isSubDev) {
        /*通过网关再转发*/
        // topics.push(topicProperty);
      }
      this.$mqttTool.unsubscribe(topics);
    },

    // 获取子组件订阅的设备状态
    getDeviceStatusData(status) {
      this.form.status = status;
    },

    // 获取直播子组件传递的激活选项卡名称
    getPlayerData(data) {
      this.activeName = data.tabName;
      this.channelId = data.channelId;
      // this.$set(this.form, 'channelId', this.channelId);
      if (this.channelId) {
        this.$refs.deviceLiveStream.channelId = this.channelId;
        this.$refs.deviceLiveStream.changeChannel();
      }
    },

    /** 选项卡改变事件*/
    tabChange(panel) {
      if (this.form.deviceType == 3 && panel.name != 'deviceReturn') {
        if (panel.name === 'sipPlayer') {
          this.$refs.deviceVideo.destroy();
          if (this.channelId) {
            this.$refs.deviceLiveStream.channelId = this.channelId;
            this.$refs.deviceLiveStream.changeChannel();
          }
          if (this.$refs.deviceLiveStream.channelId) {
            this.$refs.deviceLiveStream.changeChannel();
          }
        } else if (panel.name === 'sipVideo') {
          this.$refs.deviceLiveStream.destroy();
          if (this.$refs.deviceVideo.channelId && this.$refs.deviceVideo.queryDate) {
            this.$refs.deviceVideo.loadDevRecord();
          }
        } else {
          this.$refs.deviceVideo.destroy();
          this.$refs.deviceLiveStream.destroy();
        }
      }
      // 如果切换到运行状态选项卡且页面可见，启动定时器
      if (panel.name === 'runningStatus' && this.isPageVisible && this.form.deviceId) {
        this.startStatusTimer();
      } else {
        // 如果切换到其他选项卡，停止定时器
        this.stopStatusTimer();
      }
    },
    /** 数据同步*/
    deviceSynchronization() {
      deviceSynchronization(this.form.serialNumber).then(async (response) => {
        // 获取缓存物模型
        response.data.cacheThingsModel = await this.getCacheThingsModdel(response.data.productId);
        // 获取设备运行状态
        response.data.thingsModels = await this.getDeviceStatus(this.form);
        // 格式化物模型，拆分出监测值,数组添加前缀
        this.formatThingsModel(response.data);
        this.form = response.data;
        // 选项卡切换
        this.activeName = 'runningStatus';
        this.oldDeviceStatus = this.form.status;
        // this.loadMap();
      });
    },
    /**获取设备详情*/
    getDevice(deviceId) {
      getDevice(deviceId).then(async (response) => {
        // 分享设备获取用户权限
        response.data.userPerms = [];
        this.getDeviceStatusWitchThingsModel(response);
      });
    },
    /** 获取缓存物模型*/
    getCacheThingsModdel(productId) {
      return new Promise((resolve, reject) => {
        cacheJsonThingsModel(productId)
          .then((response) => {
            resolve(JSON.parse(response.data));
          })
          .catch((error) => {
            reject(error);
          });
      });
    },
    /**获取设备运行状态*/
    getDeviceStatus(data) {
      const params = {
        deviceId: data.deviceId,
        slaveId: data.slaveId,
      };
      return new Promise((resolve, reject) => {
        getDeviceDetectStatus(params)
          .then((response) => {
            resolve(response.data.thingsModels);
          })
          .catch((error) => {
            reject(error);
          });
      });
    },
    formatThingsModel(data) {
      data.chartList = [];
      data.monitorList = [];
      data.staticList = [];
    },
    /** 返回按钮 */
    goBack() {
      const obj = {
        path: '/test/test',
        query: {
          t: Date.now(),
          pageNum: this.$route.query.pageNum,
        },
      };
      this.$tab.closeOpenPage(obj);
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        deviceId: 0,
        deviceName: null,
        productId: null,
        productName: null,
        userId: null,
        userName: null,
        tenantId: null,
        tenantName: null,
        serialNumber: '',
        firmwareVersion: 1.0,
        status: 1,
        rssi: null,
        networkAddress: null,
        networkIp: null,
        longitude: null,
        latitude: null,
        activeTime: null,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null,
        remark: null,
        locationWay: 1,
        clientId: 0,
      };
      this.deviceStatus = 0;
      this.resetForm('form');
    },
    /** 提交按钮 */
    async submitForm() {
      if (this.form.serialNumber == null || this.form.serialNumber == 0) {
        this.$modal.alertError('设备编号不能为空');
        return;
      }
      let reg = /^[0-9a-zA-Z]+$/;
      if (!reg.test(this.form.serialNumber)) {
        this.$modal.alertError('设备编号只能是字母和数字');
        return;
      }
      if (this.form.productId == null || this.form.productId == 0) {
        this.$modal.alertError('所属产品不能为空');
        return;
      }

      this.$refs['form'].validate((valid) => {
        if (valid) {
          if (this.form.deviceId != 0) {
            updateDevice(this.form).then((response) => {
              if (response.data == 0) {
                this.$modal.alertError(response.msg);
              } else {
                this.$modal.alertSuccess('修改成功');
                this.form = JSON.parse(JSON.stringify(this.form));
                // this.loadMap();
                //是否设备设置为禁用状态，则踢出设备
                if (this.form.status === 2) {
                  const params = { clientId: this.form.serialNumber }
                  clientOut(params).then(res => { });
                }
              }
            });
          } else {
            addDevice(this.form).then(async (response) => {
              // 获取设备状态
              await this.getDeviceStatusWitchThingsModel(response);
              if (this.form.deviceId == null || this.form.deviceId == 0) {
                this.$modal.alertError('设备编号已经存在，添加设备失败');
              } else {
                if (this.form.status == 2) {
                  this.deviceStatus = 1;
                }

                this.$modal.alertSuccess('添加设备成功');
                // this.loadMap();
              }
            });
          }
        }
      });
    },
    /** 获取设备状态和物模型 **/
    async getDeviceStatusWitchThingsModel(response) {
      // 获取缓存物模型
      response.data.cacheThingsModel = await this.getCacheThingsModdel(response.data.productId);
      // 获取设备运行状态
      response.data.thingsModels = await this.getDeviceStatus(response.data);
      // 格式化物模型，拆分出监测值,数组添加前缀
      this.formatThingsModel(response.data);
      this.form = response.data;
      // 解析设备摘要
      if (this.form.summary != null && this.form.summary != '') {
        this.summary = JSON.parse(this.form.summary);
      }
      this.isSubDev = this.form.subDeviceList && this.form.subDeviceList.length > 0;
      this.oldDeviceStatus = this.form.status;
      // this.loadMap();
      //Mqtt订阅
      this.connectMqtt();
      this.mqttSubscribe(this.form);

      // 如果当前是运行状态选项卡且页面可见，启动定时器
      if (this.activeName === 'runningStatus' && this.isPageVisible) {
        this.startStatusTimer();
      }
    },
    genSipID() {
      this.$refs.sipidGen.open = true;
    },
    /**获取选中的产品 */
    getProductData(product) {
      this.form.productId = product.productId;
      this.form.productName = product.productName;
      this.form.deviceType = product.deviceType;
      this.getDeviceTemp();
      this.form.tenantId = product.tenantId;
      this.form.tenantName = product.tenantName;
      if (product.transport === 'TCP') {
        this.openServerTip = true;
        this.serverType = 3;
      } else {
        this.openServerTip = false;
        this.serverType = 1;
      }
    },
    getSipIDData(devsipid) {
      this.form.serialNumber = devsipid;
    },
    getDeviceTemp(productId) {
      getDeviceTemp(this.form).then((response) => {
        if (response.data && this.form.deviceType == 2) {
          this.openTip = true;
        } else {
          this.openTip = false;
        }
      });
    },
    // 获取选中的用户
    getUserData(user) { },
    /**关闭物模型 */
    openSummaryDialog() {
      let json = {
        type: 1, // 1=扫码关联设备
        deviceNumber: this.form.serialNumber,
        productId: this.form.productId,
        // productName: this.form.productName,
      };
      this.qrText = JSON.stringify(json);
      this.openSummary = true;
    },
    /**关闭物模型 */
    closeSummaryDialog() {
      this.openSummary = false;
      this.openViewMqtt = false;
    },
    doCopy(type) {
      if (type == 2) {
        const input = document.createElement('input');
        input.value = '{clientId:' + this.listQuery.clientId + ',username:' + this.listQuery.username + ',passwd:' + this.listQuery.passwd + ',port:' + this.listQuery.port + '}';
        document.body.appendChild(input);
        input.select(); //选中输入框
        document.execCommand('Copy'); //复制当前选中文本到前切板
        document.body.removeChild(input);
        this.$message.success('复制成功');
      }
    },
    openCodeDialog() {
      let json = {
        type: 1, // 1=扫码关联设备
        deviceNumber: this.form.serialNumber,
        productId: this.form.productId,
        productName: this.form.productName,
      };
      this.qrText = JSON.stringify(json);
      this.openCode = true;
    },
    // 地图定位
    getmap() {
      this.map = new BMap.Map('map');
      let point = null;
      if (this.form.longitude != null && this.form.longitude != '' && this.form.latitude != null && this.form.latitude != '') {
        point = new BMap.Point(this.form.longitude, this.form.latitude);
      } else {
        point = new BMap.Point(116.404, 39.915);
      }
      this.map.centerAndZoom(point, 19);
      this.map.enableScrollWheelZoom(true); // 开启鼠标滚轮缩放
      this.map.addControl(new BMap.NavigationControl());

      // 标注设备位置
      this.mk = new BMap.Marker(point);
      this.map.addOverlay(this.mk);
      this.map.panTo(point);
    },
    // 生成随机字母和数字
    generateNum() {
      if (!this.form.productId || this.form.productId == 0) {
        this.$modal.alertError('请先选择产品');
        return;
      }
      this.genDisabled = true;
      const params = { type: this.serverType };
      generatorDeviceNum(params).then((response) => {
        this.form.serialNumber = response.data;
        this.genDisabled = false;
      });
    },
    //mqtt参数查看
    handleViewMqtt() {
      this.openViewMqtt = true;
      this.loading = true;
      const params = {
        deviceId: this.form.deviceId,
      };
      getMqttConnect(params).then((response) => {
        if (response.code == 200) {
          this.listQuery = response.data;
          this.loading = false;
        }
      });
    },
  },
};
</script>
