<template>
    <el-dialog :close-on-click-modal="false" title="设备回收记录" :visible.sync="open" width="800px">
        <div style="margin-top:-55px;">
            <el-divider style="margin-top:-30px;"></el-divider>
            <el-form :model="queryParams" ref="queryForm" :inline="true" label-width="68px">
                <el-form-item prop="deptId">
                    <treeselect v-model="queryParams.deptId" :options="deptOptions" :show-count="true"
                        style="width:180px" placeholder="机构名称" />
                </el-form-item>
                <el-form-item prop="productId">
                    <el-select v-model="queryParams.productId" placeholder="产品名称" style="width: 180px" filterable>
                        <el-option v-for="item in productList" :key="item.value" :label="item.label"
                            :value="item.value">
                        </el-option>
                    </el-select>
                </el-form-item>
                <el-form-item prop="serialNumber">
                    <el-input v-model="queryParams.serialNumber" placeholder="设备编号" clearable size="small"
                        style="width:180px" @keyup.enter.native="handleQuery" />
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
                    <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
                </el-form-item>
            </el-form>

            <el-table v-loading="loading" ref="singleTable" :data="dataList" size="mini">
                <el-table-column label="机构名称" align="left" />
                <el-table-column label="被回收机构名称" align="left" min-width="120" />
                <el-table-column label="设备名称" align="left" />
                <el-table-column label="DeviceKey" align="left" />
                <el-table-column label="设备编号" align="left" />
                <el-table-column label="产品名称" align="left" />
                <el-table-column label="回收时间" align="left" />
            </el-table>
            <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum"
                :limit.sync="queryParams.pageSize" />
            <!-- @pagination="getList"  -->
        </div>
    </el-dialog>
</template>

<script>
import {
    listProduct
} from "@/api/iot/product";
import {
    listRecycleRecord
} from "@/api/iot/device";
import { deptsTreeSelect } from "@/api/system/user";
import Treeselect from "@riophae/vue-treeselect";
import "@riophae/vue-treeselect/dist/vue-treeselect.css";

export default {
    name: "recycleRecord",
    dicts: [],
    components: {
        Treeselect
    },
    data() {
        return {
            // 遮罩层
            loading: true,
            // 总条数
            total: 0,
            // 打开选择产品对话框
            open: false,
            // 产品列表
            productList: [],
            statusList: [],
            dataList: [],
            //时间范围
            daterangeTime: [],
            deptOptions: [],
            // 查询参数
            queryParams: {
                pageNum: 1,
                pageSize: 10,
                productName: null,
                serialNumber: '',

            },
        };
    },
    created() {
        this.getProductList();
        this.getDeptTree();
    },
    methods: {
        /** 查询产品列表 */
        getProductList() {
            this.loading = true;
            const params = {
                pageSize: 999,
            }
            listProduct(params).then(response => {
                this.productList = response.rows.map((item) => {
                    return { value: item.productId, label: item.productName };
                });
                this.loading = false;
            });
        },
        //查询导入记录列表
        getList() {
            this.loading = true;
            listRecycleRecord().then(response => {
                this.dataList = response.rows;
                this.toltal = response.total;
                this.loading = false;
            });
        },
        /** 查询机构下拉树结构 */
        getDeptTree() {
            deptsTreeSelect().then(response => {
                this.deptOptions = response.data;
            });
        },
        /** 搜索按钮操作 */
        handleQuery() {
            this.queryParams.pageNum = 1;
            this.getList();
        },
        /** 重置按钮操作 */
        resetQuery() {
            this.resetForm("queryForm");
            this.handleQuery();
        },
        /**关闭对话框 */
        closeDialog() {
            this.open = false;
        }
    }
};
</script>
