import request from '@/utils/request';

// 查询所有设备简短列表
export function getAllShortDevice() {
    return request({
        url: '/iot/device/all',
        method: 'get',
    });
}

// 根据serialNumber查询设备模型属性列表
export function getDeviceModel(serialNumber) {
    return request({
        url: '/iot/device/listModelProperties/' + serialNumber,
        method: 'get',
    });
}

// 根据查询参数展示列表
export function getModelData(query) {
    return request({
        url: '/iot/deviceLog/historyTable',
        method: 'get',
        params: query,
    });
}
