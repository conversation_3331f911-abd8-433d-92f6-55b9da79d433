<template>
  <div class="app-container">
    <el-row :gutter="20">
      <!--公共菜单数据-->
      <el-col :span="4">
        <div class="head-container">
          <el-input v-model="deptName" placeholder="请输入菜单名称" clearable size="small" prefix-icon="el-icon-search"
            style="margin-bottom: 20px" />
        </div>
        <div class="head-container">
          <el-tree :data="deptOptions" :props="defaultProps" :expand-on-click-node="false"
            :filter-node-method="filterNode" ref="tree" node-key="id" default-expand-all highlight-current
            @node-click="handleNodeClick" />
        </div>
      </el-col>

      <!--用户数据-->
      <el-col :span="20">
        <div class="card-container">
          <el-card :body-style="{ width: '100%', padding: '0', height: '92vh' }">
            <el-tabs v-model="activeName" type="card" class="demo-tabs" @tab-click="handleClick">
              <el-tab-pane label="分布图" name="plan" style="padding: 0px 6px 6px 6px; height: 85.4vh;">
                <el-empty style="margin: 200px;" description="暂无数据" v-if="geographyUrl == ''"></el-empty>
                <div class="draggable-image-container" v-show="geographyUrl != ''">
                  <el-image class="geography_image" :src="baseApi + geographyUrl" fit="cover" />
                  <div v-for="(item, index) in items" :key="index"
                    :style="{ left: item.dragStartX + 'px', top: item.dragStartY + 'px' }" class="draggable-image">
                    <el-popover placement="right-start" :width="220" trigger="click">
                      <template #reference>
                        <el-tooltip class="box-item" effect="dark" content="电梯厅射灯1-节能-反馈" placement="right-start">
                          <img :src="baseApi + item.resourceUrl" alt="Draggable Image">
                        </el-tooltip>
                      </template>

                      <el-row>
                        <el-col :span="24"
                          style="text-align: center;  margin-bottom: 8px; font-weight: 800;"><span>射灯1-节能-反馈</span></el-col>
                        <el-col class="pilot-lamp" :span="24">
                          <span>执行动作：</span>
                          <el-switch v-model="switchState" active-color="#13ce66" inactive-color="#ff4949">
                          </el-switch>
                        </el-col>
                        <el-col class="pilot-lamp" :span="24" style="margin-top: 10px;">
                          <span>运行时常：10H</span>
                        </el-col>
                        <el-col class="pilot-lamp" :span="24" style="margin-top: 10px;">
                          <span>故障报警：否</span>
                        </el-col>
                      </el-row>
                    </el-popover>
                  </div>
                </div>
              </el-tab-pane>
              <el-tab-pane label="回路列表" name="overview" style="height: 82.4vh;">
                <div style="width: 100%; height: 100%;" class="light-container">
                  <div class="light-control-normal">
                    <div
                      style="width: 100%; height: 34px; line-height: 34px; padding: 0 10px; display: flex; justify-content: space-between; ">
                      <el-text class="name">照明-01</el-text>
                      <el-text class="record" @click="controlRecords">
                        <svg style="width: 20px; height: 20px; margin-top: 8px;" t="1728974035571" class="icon"
                          viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="4477"
                          width="200" height="200">
                          <path
                            d="M778.24 61.44a122.88 122.88 0 0 1 122.88 122.88v655.36a122.88 122.88 0 0 1-122.88 122.88H245.76a122.88 122.88 0 0 1-122.88-122.88V184.32a122.88 122.88 0 0 1 122.88-122.88h532.48z m0 61.44H245.76a61.44 61.44 0 0 0-61.3376 57.83552L184.32 184.32v655.36a61.44 61.44 0 0 0 57.83552 61.3376L245.76 901.12h532.48a61.44 61.44 0 0 0 61.3376-57.83552L839.68 839.68V184.32a61.44 61.44 0 0 0-57.83552-61.3376L778.24 122.88zM563.2 532.48a30.72 30.72 0 0 1 0 61.44h-266.24a30.72 30.72 0 0 1 0-61.44h266.24z m163.84-225.28a30.72 30.72 0 0 1 0 61.44h-430.08a30.72 30.72 0 0 1 0-61.44h430.08z"
                            fill="#64B735" p-id="4478"></path>
                        </svg>
                      </el-text>
                    </div>
                    <div
                      style="width: 100%; height: 70px; padding: 0 8px; display: flex; justify-content: space-between; ">
                      <svg id="sign" t="1729040820154" class="icon" viewBox="0 0 1024 1024" version="1.1"
                        xmlns="http://www.w3.org/2000/svg" p-id="11823" width="200" height="200">
                        <path
                          d="M512 268.258743c198.597486 0 362.993371 149.182171 387.072 341.343086H124.928c24.078629-192.160914 188.474514-341.343086 387.072-341.343086m0-48.771657c-242.366171 0-438.857143 196.490971-438.857143 438.857143h877.714286c0-242.366171-196.490971-438.857143-438.857143-438.857143z"
                          fill="#64B735" p-id="11824"></path>
                        <path
                          d="M652.141714 657.642057c-20.216686 57.929143-75.395657 99.620571-140.141714 99.620572s-119.925029-41.691429-140.141714-99.620572h280.283428m56.992915-48.771657H314.865371a197.134629 197.134629 0 0 0 394.269258 0zM487.6288 874.320457h48.771657V950.857143h-48.771657zM665.014857 823.471543l38.268343 66.296686-42.218057 24.3712-38.268343-66.296686zM750.943086 715.220114l66.267428 38.268343-24.400457 42.247314-66.267428-38.2976zM359.043657 823.442286l42.1888 24.400457-38.326857 66.296686-42.1888-24.400458zM272.969143 715.190857l24.3712 42.218057-66.267429 38.268343-24.400457-42.247314z"
                          fill="#64B735" p-id="11825"></path>
                        <path d="M487.6288 73.142857h48.771657v164.688457h-48.771657z" fill="#64B735" p-id="11826">
                        </path>
                      </svg>
                      <div style="width: 50px; height: 60px; line-height: 8px; ">
                        <img class="img" :src="baseApi + '/profile/other/normal.svg'" />
                        <el-text style="font-size: 12px;">运行正常</el-text>
                      </div>
                    </div>
                  </div>

                  <div class="light-control-close">
                    <div
                      style="width: 100%; height: 34px; line-height: 34px; padding: 0 10px; display: flex; justify-content: space-between; ">
                      <el-text class="name">照明-02</el-text>
                      <el-text class="record" @click="controlRecords">
                        <svg style="width: 20px; height: 20px; margin-top: 8px;" t="1728974035571" class="icon"
                          viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="4477"
                          width="200" height="200">
                          <path
                            d="M778.24 61.44a122.88 122.88 0 0 1 122.88 122.88v655.36a122.88 122.88 0 0 1-122.88 122.88H245.76a122.88 122.88 0 0 1-122.88-122.88V184.32a122.88 122.88 0 0 1 122.88-122.88h532.48z m0 61.44H245.76a61.44 61.44 0 0 0-61.3376 57.83552L184.32 184.32v655.36a61.44 61.44 0 0 0 57.83552 61.3376L245.76 901.12h532.48a61.44 61.44 0 0 0 61.3376-57.83552L839.68 839.68V184.32a61.44 61.44 0 0 0-57.83552-61.3376L778.24 122.88zM563.2 532.48a30.72 30.72 0 0 1 0 61.44h-266.24a30.72 30.72 0 0 1 0-61.44h266.24z m163.84-225.28a30.72 30.72 0 0 1 0 61.44h-430.08a30.72 30.72 0 0 1 0-61.44h430.08z"
                            fill="#909399" p-id="4478"></path>
                        </svg>
                      </el-text>
                    </div>
                    <div
                      style="width: 100%; height: 70px; padding: 0 8px; display: flex; justify-content: space-between; ">
                      <svg id="sign" t="1729040820154" class="icon" viewBox="0 0 1024 1024" version="1.1"
                        xmlns="http://www.w3.org/2000/svg" p-id="11823" width="200" height="200">
                        <path
                          d="M512 268.258743c198.597486 0 362.993371 149.182171 387.072 341.343086H124.928c24.078629-192.160914 188.474514-341.343086 387.072-341.343086m0-48.771657c-242.366171 0-438.857143 196.490971-438.857143 438.857143h877.714286c0-242.366171-196.490971-438.857143-438.857143-438.857143z"
                          fill="#909399" p-id="11824"></path>
                        <path
                          d="M652.141714 657.642057c-20.216686 57.929143-75.395657 99.620571-140.141714 99.620572s-119.925029-41.691429-140.141714-99.620572h280.283428m56.992915-48.771657H314.865371a197.134629 197.134629 0 0 0 394.269258 0zM487.6288 874.320457h48.771657V950.857143h-48.771657zM665.014857 823.471543l38.268343 66.296686-42.218057 24.3712-38.268343-66.296686zM750.943086 715.220114l66.267428 38.268343-24.400457 42.247314-66.267428-38.2976zM359.043657 823.442286l42.1888 24.400457-38.326857 66.296686-42.1888-24.400458zM272.969143 715.190857l24.3712 42.218057-66.267429 38.268343-24.400457-42.247314z"
                          fill="#909399" p-id="11825"></path>
                        <path d="M487.6288 73.142857h48.771657v164.688457h-48.771657z" fill="#909399" p-id="11826">
                        </path>
                      </svg>
                      <div style="width: 50px; height: 60px; line-height: 8px; ">
                        <img class="img" :src="baseApi + '/profile/other/close.svg'" />
                        <el-text style="font-size: 12px;">设备关闭</el-text>
                      </div>
                    </div>
                  </div>

                  <div class="light-control-fault">
                    <div
                      style="width: 100%; height: 34px; line-height: 34px; padding: 0 10px; display: flex; justify-content: space-between; ">
                      <el-text class="name">照明-03</el-text>
                      <el-text class="record" @click="controlRecords">
                        <svg style="width: 20px; height: 20px; margin-top: 8px;" t="1728974035571" class="icon"
                          viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="4477"
                          width="200" height="200">
                          <path
                            d="M778.24 61.44a122.88 122.88 0 0 1 122.88 122.88v655.36a122.88 122.88 0 0 1-122.88 122.88H245.76a122.88 122.88 0 0 1-122.88-122.88V184.32a122.88 122.88 0 0 1 122.88-122.88h532.48z m0 61.44H245.76a61.44 61.44 0 0 0-61.3376 57.83552L184.32 184.32v655.36a61.44 61.44 0 0 0 57.83552 61.3376L245.76 901.12h532.48a61.44 61.44 0 0 0 61.3376-57.83552L839.68 839.68V184.32a61.44 61.44 0 0 0-57.83552-61.3376L778.24 122.88zM563.2 532.48a30.72 30.72 0 0 1 0 61.44h-266.24a30.72 30.72 0 0 1 0-61.44h266.24z m163.84-225.28a30.72 30.72 0 0 1 0 61.44h-430.08a30.72 30.72 0 0 1 0-61.44h430.08z"
                            fill="#ff0000" p-id="4478"></path>
                        </svg>
                      </el-text>
                    </div>
                    <div
                      style="width: 100%; height: 70px; padding: 0 8px; display: flex; justify-content: space-between; ">
                      <svg id="sign" t="1729040820154" class="icon" viewBox="0 0 1024 1024" version="1.1"
                        xmlns="http://www.w3.org/2000/svg" p-id="11823" width="200" height="200">
                        <path
                          d="M512 268.258743c198.597486 0 362.993371 149.182171 387.072 341.343086H124.928c24.078629-192.160914 188.474514-341.343086 387.072-341.343086m0-48.771657c-242.366171 0-438.857143 196.490971-438.857143 438.857143h877.714286c0-242.366171-196.490971-438.857143-438.857143-438.857143z"
                          fill="#ff0000" p-id="11824"></path>
                        <path
                          d="M652.141714 657.642057c-20.216686 57.929143-75.395657 99.620571-140.141714 99.620572s-119.925029-41.691429-140.141714-99.620572h280.283428m56.992915-48.771657H314.865371a197.134629 197.134629 0 0 0 394.269258 0zM487.6288 874.320457h48.771657V950.857143h-48.771657zM665.014857 823.471543l38.268343 66.296686-42.218057 24.3712-38.268343-66.296686zM750.943086 715.220114l66.267428 38.268343-24.400457 42.247314-66.267428-38.2976zM359.043657 823.442286l42.1888 24.400457-38.326857 66.296686-42.1888-24.400458zM272.969143 715.190857l24.3712 42.218057-66.267429 38.268343-24.400457-42.247314z"
                          fill="#ff0000" p-id="11825"></path>
                        <path d="M487.6288 73.142857h48.771657v164.688457h-48.771657z" fill="#ff0000" p-id="11826">
                        </path>
                      </svg>
                      <div style="width: 50px; height: 60px; line-height: 8px; ">
                        <img class="img" :src="baseApi + '/profile/other/error.svg'" />
                        <el-text style="font-size: 12px;">运行故障</el-text>
                      </div>
                    </div>
                  </div>
                </div>
              </el-tab-pane>
            </el-tabs>
            <!-- <div v-for="(image, index) in images" :key="index" class="draggable-image" >
                <el-image
                  :src="baseApi + image.resourceUrl"
                  :draggable="isDraggable" fit="cover"
                />
              </div> -->
            <!-- <el-skeleton-item v-show="geographyUrl == ''" variant="image" description="暂无数据" style="width: 100%; height: 100%;" /> -->
            <!-- <el-empty description="暂无数据" v-if="geographyUrl == ''"></el-empty> -->
          </el-card>
        </div>
      </el-col>
    </el-row>

    <!-- 选择设备弹窗 -->
    <el-dialog :close-on-click-modal="false" :title="title" v-if="open" :visible.sync="open" center width="1200px">
      <OperationRecord />
    </el-dialog>
  </div>
</template>

<script>
import { listGallery } from '@/api/subsystem/gallery';//加载所有状态为 1正常 的图标
import { findTiePointDeviceByTiePointId } from "@/api/subsystem/tiepointDevice";//查询子系统的绑点关联关系
import { getDevice } from "@/api/iot/device";//查询设备详情
import { getDicts } from '@/api/system/dict/data';
import { deptTreeSelect, getGeography } from "@/api/subsystem/geography";
import { monitorReportStatistics } from "@/api/system/floor";
import { findTiePointByfloorIdAndCategoryName } from "@/api/subsystem/tiepoint";//查询绑定的点位
import Treeselect from "@riophae/vue-treeselect";
import "@riophae/vue-treeselect/dist/vue-treeselect.css";
import draggable from 'vuedraggable'
import OperationRecord from "./operation-record";//组态JSON

export default {
  name: "User",
  dicts: ['subsystem_gallery_icon_type', 'sys_floor_type', 'iot_device_status'],
  components: {
    Treeselect,
    draggable,
    OperationRecord
  },
  data() {
    return {
      // 遮罩层
      loading: true,
      switchState: true,
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 根路径
      baseApi: process.env.VUE_APP_BASE_API,
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      galleryList: [],  // 图库管理表格数据
      geographyUrl: "", // 楼层CAD背景图

      // 部门树选项
      deptOptions: undefined,
      // 部门名称
      deptName: undefined,
      // 日期范围
      dateRange: [],
      // 岗位选项
      postOptions: [],
      // 角色选项
      roleOptions: [],
      defaultProps: {
        children: "children",
        label: "label"
      },
      //模块类型查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 20,
        categoryName: '',
        galleryName: '',
        resourceUrl: null,
        status: null,
        isAsc: 'desc',
      },
      queryGeographyParams: {
        floorId: undefined,
        galleryId: undefined,
        dragStartX: 0,
        dragStartY: 0
      },
      items: [],
      draggingIndex: null,
      dragStartAxleX: 0,
      dragStartAxleY: 0,
      dragGaTiepointId: undefined,
      monitorFloorId: 0,//选中的菜单编号
      monitorItems: [],
      monitorInfo: [],
      videoUrl: [''],
      spilt: 1, //分屏
      playerIdx: 0, //激活播放器
      updateLooper: 0, //数据刷新轮训标志
      count: 15,
      total: 0,
      loading: false,
      activeName: 'plan'
    };
  },
  watch: {
    // 根据名称筛选部门树
    deptName(val) {
      this.$refs.tree.filter(val);
    }
  },
  created() {
    this.getDeptTree();//页面初始化查询左侧公共菜单
    // 获取数据
    this.getDatas();
    // this.getGeographyUrl();
    //查询视频监控统计报表
    this.getMonitorReportStatistics();
  },
  methods: {
    //控制记录
    controlRecords() {
      this.title = "【射灯1-节能-反馈】";
      this.open = true;
    },
    getMonitorReportStatistics() {
      if (this.queryGeographyParams.floorId != undefined) {
        this.monitorFloorId = this.queryGeographyParams.floorId;
      }
      monitorReportStatistics(this.monitorFloorId).then((response) => {
        if (response.code === 200) {
          this.monitorItems = response.data;
        }
      });
    },
    //-----------------------------------------------------------------------------------------------------------------------------------------------------------
    //获取子系统图标
    async getDatas() {
      //const dictType = 'subsystem_gallery_icon_type';
      //const { data } = (await this.getCategoryTypes(dictType)) || [];
      this.queryParams.categoryName = '智能照明';
      this.queryParams.status = "1";
      this.getGalleryList();
    },
    // 获取种类
    getCategoryTypes(dictType) {
      return new Promise((resolve, reject) => {
        getDicts(dictType)
          .then((res) => {
            resolve(res);
          })
          .catch((error) => {
            reject(error);
          });
      });
    },
    getGalleryList() {
      this.loading = true;
      listGallery(this.queryParams).then((response) => {
        if (response.code === 200) {
          this.galleryList = response.rows;
          this.total = response.total;
        }
        this.loading = false;
      });
    },
    //模块类型选择
    handleGalleryChange(value) {
      this.queryParams.pageNum = 1;
      this.queryParams.categoryName = value;
      this.queryParams.status = "1";
      this.single = true;
      this.multiple = true;
      this.getGalleryList();
      this.queryCoordinate();
    },


    //-----------------------------------------------------------------------------------------------------------------------------------------------------------
    /** 查询部门下拉树结构 */
    getDeptTree() {
      deptTreeSelect().then(response => {
        this.deptOptions = response.data;
        this.queryGeographyParams.floorId = this.deptOptions[0].id;//获取左侧tree的根节点传给右侧查询楼层cad背景图
        this.getGeographyUrl();
      });
    },
    // 节点单击事件
    handleNodeClick(data) {
      this.queryGeographyParams.floorId = data.id;
      this.getGeographyUrl();
      this.getMonitorReportStatistics();
    },

    handleClick(tab, event) {
      console.log(tab, event);
      // 切换标签页时的逻辑处理
    },

    /** 查询楼层CAD背景图 */
    getGeographyUrl() {
      this.loading = true;
      getGeography(this.queryGeographyParams.floorId).then(response => {
        //判断查询菜单是否设置了背景图
        if (response.data == undefined) {
          this.geographyUrl = ""
        } else {
          this.geographyUrl = response.data.geographyUrl;
          this.queryCoordinate();
        }
      });
    },
    queryCoordinate() {
      //alert("根据该节点编号查询关于他的所有子系统图标===="+floorId);
      findTiePointByfloorIdAndCategoryName(this.queryGeographyParams.floorId, this.queryParams.categoryName).then(response => {
        this.items = response.data;
      });
    },
    // 筛选节点
    filterNode(value, data) {
      if (!value) return true;
      return data.label.indexOf(value) !== -1;
    },
  }
};
</script>

<style scoped>
.grid-content {
  height: 5.5vh;
  margin-top: 1vh;
}

.grid-content span {
  color: #E5EFF0;
  line-height: 5.5vh;
  margin-left: 1vw;
}

.grid-content span a {
  font-size: 20px;
  font-weight: 800;
}

.btn {
  margin: 0 10px;
}

.btn:hover {
  color: #409eff;
}

.btn.active {
  color: #409eff;
}

.redborder {
  border: 2px solid red !important;
}

.play-box {
  background-color: #000000;
  border: 1px solid #505050;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 0px;
  position: relative;
  border-radius: 5px;
}

.player-wrap {
  position: absolute;
  top: 0px;
  height: 100% !important;
  width: 100% !important;
}

/* ---------------------------------------------------------------------------------------------------------------------------------------------- */
::v-deep .el-dialog__body {
  padding: 8px 14px 14px 14px;
}

.el-descriptions img {
  width: 30px;
  height: 30px;
  position: absolute;
  margin-top: 1px;
}

.el-descriptions .el-tag {
  font-size: 14px;
  margin: 2px 0 0 34px;
}

.el-descriptions .mx-1 {
  font-size: 14px;
  font-weight: 700;
  margin-left: 4px;
  color: #888888;
}

:deep(.my-label) {
  background: var(--el-color-success-light-9) !important;
}

:deep(.my-content) {
  background: var(--el-color-danger-light-9);
}

.demo_image_preview {
  display: flex;
  /* 使div变为Flex容器 */
  flex-wrap: nowrap;
  /* 避免图片换行 */
}

.draggable-image-container {
  position: relative;
}

.geography_image {
  /** 楼层背景样式 */
  width: 100%;
  height: 85.4vh;
  z-index: 1;
  display: block;
}

.draggable-image {
  position: absolute;
  cursor: pointer;
  z-index: 999;
}

.draggable-image img {
  width: 40px;
  height: 45px;
}

.el-row:last-child {
  margin-bottom: 0;
}

::v-deep .el-tabs__header {
  margin: 0 0 6px;
}

::v-deep .el-tabs__item {
  height: 5vh;
  line-height: 5vh;
  font-weight: 600;
}

.light-container {
  display: grid;
  grid-template-columns: repeat(6, 1fr);
  /* 每行6个等宽的列 */
  grid-auto-rows: 105px;
  /* 每个grid item的高度为120px */
  row-gap: 20px;
  /* 行间距 */
  column-gap: 20px;
  /* 列间距设为20px */
  width: 100%;
  /* 给容器加上内外边距，确保边缘也有20px的间距 */
  padding: 14px 20px 0 20px;
  cursor: pointer;
  font-family: '微软雅黑';
}

/* 正常 */
.light-control-normal {
  border: 1px #64B735 solid;
  /* 给div加上内边距，确保内容距离边界也有20px */
  /* padding: 20px;  */
  color: #64B735;
  text-align: center;
  /* 文字居中（如果只有一个元素时） */
  /* padding: 0 12px; */
  line-height: 34px;
}

/* 关闭 */
.light-control-close {
  border: 1px #909399 solid;
  /* 给div加上内边距，确保内容距离边界也有20px */
  /* padding: 20px;  */
  color: #909399;
  text-align: center;
  /* 文字居中（如果只有一个元素时） */
  line-height: 34px;
}

/* 关闭 */
.light-control-fault {
  border: 1px #ff0000 solid;
  /* 给div加上内边距，确保内容距离边界也有20px */
  /* padding: 20px;  */
  color: #ff0000;
  text-align: center;
  /* 文字居中（如果只有一个元素时） */
  text-align: center;
  /* 文字居中（如果只有一个元素时） */
  /* padding: 0 12px; */
}

#sign {
  margin-top: 14px;
  width: 45px;
  height: 45px;
}

.light-container img {
  margin-top: 8px;
  width: 40px;
  height: 40px;
}
</style>
