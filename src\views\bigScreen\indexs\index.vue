<template>
  <div class="contents">
    <div class="contents_left">
      <div class="statistical-report">
        <dv-border-box-13 :color="['#337ecc', '#337ecc']">
          <div class="item_title">
            <div class="left_side"></div>
            <p>设备总览</p>
            <div class="right_side"></div>
          </div>
          <div class="item_report_form">
            <LeftTop />
          </div>
        </dv-border-box-13>
      </div>
      <div class="statistical-report">
        <dv-border-box-13 :color="['#337ecc', '#337ecc']">
          <div class="item_title">
            <div class="left_side"></div>
            <p>Mqtt统计</p>
            <div class="right_side"></div>
          </div>
          <div class="item_report_form">
            <DynamicLoopDiagram />
          </div>
        </dv-border-box-13>
      </div>
      <div class="statistical-report">
        <dv-border-box-13 :color="['#337ecc', '#337ecc']">
          <div class="item_title">
            <div class="left_side"></div>
            <p>监测数据</p>
            <div class="right_side"></div>
          </div>
          <div class="item_report_form">
            <DataMonitoring />
          </div>
        </dv-border-box-13>
      </div>
    </div>

    <div class="contents_center">
      <!-- <Glb3dModel /> -->
      <centermap />
    </div>

    <div class="contents_right">
      <div class="statistical-report">
        <dv-border-box-13 :color="['#337ecc', '#337ecc']">
          <div class="item_title">
            <div class="left_side"></div>
            <p>Mqtt状态</p>
            <div class="right_side"></div>
          </div>
          <div class="item_report_form">
            <NightingaleChart />
          </div>
        </dv-border-box-13>
      </div>
      <div class="statistical-report">
        <dv-border-box-13 :color="['#337ecc', '#337ecc']">
          <div class="item_title">
            <div class="left_side"></div>
            <p>Mqtt消息</p>
            <div class="right_side"></div>
          </div>
          <div class="item_report_form">
            <GarageTraffic />
          </div>
        </dv-border-box-13>
      </div>
      <div class="statistical-report">
        <dv-border-box-13 :color="['#337ecc', '#337ecc']">
          <div class="item_title">
            <div class="left_side"></div>
            <p>设备提醒</p>
            <div class="right_side"></div>
          </div>
          <div class="item_report_form">
            <MonitorEquipment />
          </div>
        </dv-border-box-13>
      </div>
    </div>
  </div>
</template>

<script>
import CountTo from 'vue-count-to';
import LeftTop from './left-top.vue'; //设备总览
import Glb3dModel from './glb3d-model.vue'; //加载glb文件
import centermap from './center_map.vue';
import GarageTraffic from './garage-traffic.vue'; //车库
import DynamicLoopDiagram from './dynamicloopdiagram.vue'; //能耗
import NightingaleChart from './nightingale-chart.vue'; //能耗
import MonitorEquipment from './monitor-equipment.vue'; //监控
import DataMonitoring from './data-monitoring.vue'; //监测
export default {
  components: {
    LeftTop,
    GarageTraffic,
    DynamicLoopDiagram,
    NightingaleChart,
    MonitorEquipment,
    DataMonitoring,
    Glb3dModel,
    centermap,
  },
  data() {
    return {};
  },
  filters: {
    numsFilter(msg) {
      return msg || 0;
    },
  },
  created() { },

  mounted() { },
  methods: {},
};
</script>

<style lang="scss" scoped>
// 内容
.contents {
  width: 100%;
  height: 92vh;

  .contents_left {
    width: 20vw;
    height: 92vh;
    float: left;
    z-index: 999;
    // margin-left: 0.5vw;
    margin-top: 8vh;
    position: relative;
  }

  // 中间
  .contents_center {
    z-index: 1;
    height: 100vh;
    width: 100%;
    position: absolute;
    /* 其他背景属性，如需要的话 */
    background-position: center center;
    /* 背景图片位置 */
    background-repeat: no-repeat;
    /* 不重复背景图片 */
    background-size: 100% 100%;
    overflow: visible;
  }

  .contents_right {
    width: 20vw;
    height: 92vh;
    float: right;
    z-index: 999;
    //margin-right: 0.5vw;
    margin-top: 8vh;
    position: relative;
  }

  .contents_left .statistical-report,
  .contents_right .statistical-report {
    height: 30vh;
    width: 20vw;
    z-index: 999;
    margin-top: 0.26vw;
  }

  .contents_left .statistical-report {
    background-color: transparent;
    background: linear-gradient(to right, #13465a 10%, rgba(255, 255, 255, 0.01) 100%);
    backdrop-filter: blur(2px);
  }

  .contents_right .statistical-report {
    background-color: transparent;
    background: linear-gradient(to left, #13465a 10%, rgba(255, 255, 255, 0.01) 100%);
    backdrop-filter: blur(2px);
  }

  .item_title {
    height: 5vh;
    line-height: 5vh;
    width: 100%;
    font-size: 0.8vw;
    color: #31abe3;
    text-align: center;
    position: relative;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
  }

  .left_side {
    width: 3vw;
    height: 0.68vw;
    // background-image: url(data:image/png;base64,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);
  }

  .item_title p {
    line-height: 5.4vh;
    font-weight: 900;
    letter-spacing: 1px;
    background: linear-gradient(92deg, #0072ff, #00eaff 48.8525390625%, #01aaff);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
  }

  .right_side {
    width: 3vw;
    height: 0.68vw;
    // background-image: url(data:image/png;base64,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);
    transform: rotate(180deg);
  }

  .item_report_form {
    width: 100%;
    height: 24.5vh;
  }
}
</style>
